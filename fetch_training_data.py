"""
🚀 TRAINING DATA FETCHER FOR BRAIN MODEL 🚀
==============================================

Fetches 10 different assets with 20 days of historical data using Quotex API
Saves both raw and processed data per asset for training

Features:
- Quotex API integration for real trading data
- 10 diverse assets (Forex, Crypto, Stocks)
- Raw and processed data saving
- Data validation and quality checks
- Ready for Google Colab training

Author: Trading Bot AI
Version: 2.0.0 - Quotex API Integration
License: MIT
"""

import os
import pandas as pd
import numpy as np
import asyncio
import json
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')

from sklearn.preprocessing import MinMaxScaler
import logging

# Import Quotex API
from quotexapi.stable_api import Quotex

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingDataFetcher:
    """
    🚀 Training Data Fetcher for Brain Model

    Fetches historical data for multiple assets and prepares training datasets
    """

    def __init__(self, data_dir="training_data", email=None, password=None):
        """
        Initialize the data fetcher

        Args:
            data_dir: Directory to save training data
            email: Quotex account email
            password: Quotex account password
        """
        self.data_dir = data_dir
        self.raw_data_dir = os.path.join(data_dir, "raw")
        self.processed_data_dir = os.path.join(data_dir, "processed")

        # Create directories
        os.makedirs(self.raw_data_dir, exist_ok=True)
        os.makedirs(self.processed_data_dir, exist_ok=True)

        # Quotex credentials
        self.email = email
        self.password = password
        self.client = None

        # Asset configuration (Quotex format)
        self.assets = {
            # Forex pairs (Quotex format)
            'EURUSD': 'EURUSD_otc',
            'GBPUSD': 'GBPUSD_otc',
            'USDJPY': 'USDJPY_otc',
            'AUDUSD': 'AUDUSD_otc',
            'USDCAD': 'USDCAD_otc',

            # Cryptocurrencies
            'BTCUSD': 'BTCUSD_otc',
            'ETHUSD': 'ETHUSD_otc',
            'XRPUSD': 'XRPUSD_otc',

            # Stocks
            'MSFT': 'MSFT_otc',
            'GOOGL': 'GOOGL_otc'
        }

        self.days = 20
        self.period = 60  # 1-minute candles (60 seconds)

        logger.info(f"✅ TrainingDataFetcher initialized")
        logger.info(f"📁 Data directory: {self.data_dir}")
        logger.info(f"🎯 Assets to fetch: {len(self.assets)}")
        logger.info(f"🔗 Using Quotex API for data collection")

    async def connect_quotex(self):
        """
        Connect to Quotex API

        Returns:
            bool: True if connected successfully
        """
        try:
            if not self.email or not self.password:
                logger.error("❌ Quotex credentials not provided")
                return False

            logger.info("🔗 Connecting to Quotex API...")

            self.client = Quotex(
                email=self.email,
                password=self.password,
                lang="en"
            )

            # Connect to Quotex
            check_connect, reason = await self.client.connect()

            if check_connect:
                logger.info("✅ Successfully connected to Quotex API")

                # Switch to demo account for safety
                await self.client.change_account("PRACTICE")
                logger.info("🎯 Switched to DEMO account")

                return True
            else:
                logger.error(f"❌ Failed to connect to Quotex: {reason}")
                return False

        except Exception as e:
            logger.error(f"❌ Error connecting to Quotex: {str(e)}")
            return False

    async def fetch_quotex_data(self, symbol, asset_name, days=20):
        """
        Fetch data from Quotex API

        Args:
            symbol: Quotex asset symbol
            asset_name: Friendly asset name
            days: Number of days of historical data

        Returns:
            DataFrame with OHLC data
        """
        try:
            logger.info(f"📊 Fetching {asset_name} ({symbol}) from Quotex API...")

            if not self.client:
                logger.error("❌ Quotex client not connected")
                return None

            # Calculate time parameters
            end_time = time.time()
            offset = days * 24 * 60 * 60  # Convert days to seconds

            # Try multiple methods to fetch candles (same as in bot.py)
            candles = None

            # Method 1: Standard get_candles
            try:
                candles = await asyncio.wait_for(
                    self.client.get_candles(symbol, end_time, offset, self.period),
                    timeout=10.0
                )
                if candles and len(candles) > 0:
                    logger.info(f"✅ Method 1 success for {asset_name}: {len(candles)} candles")
                else:
                    candles = None
            except Exception as e:
                logger.debug(f"Method 1 failed for {asset_name}: {str(e)}")
                candles = None

            # Method 2: Try get_candle_v2 if first method fails
            if not candles:
                try:
                    candles = await asyncio.wait_for(
                        self.client.get_candle_v2(symbol, self.period),
                        timeout=10.0
                    )
                    if candles and len(candles) > 0:
                        logger.info(f"✅ Method 2 success for {asset_name}: {len(candles)} candles")
                    else:
                        candles = None
                except Exception as e:
                    logger.debug(f"Method 2 failed for {asset_name}: {str(e)}")
                    candles = None

            # Method 3: Try without _otc suffix
            if not candles and "_otc" in symbol:
                try:
                    clean_symbol = symbol.replace("_otc", "")
                    candles = await asyncio.wait_for(
                        self.client.get_candles(clean_symbol, end_time, offset, self.period),
                        timeout=10.0
                    )
                    if candles and len(candles) > 0:
                        logger.info(f"✅ Method 3 success for {clean_symbol}: {len(candles)} candles")
                    else:
                        candles = None
                except Exception as e:
                    logger.debug(f"Method 3 failed for {clean_symbol}: {str(e)}")
                    candles = None

            if not candles:
                logger.warning(f"⚠️ No data found for {asset_name}")
                return None

            # Convert to DataFrame (same logic as in bot.py)
            df_data = []
            for candle in candles:
                try:
                    # Handle different candle data formats
                    if isinstance(candle, dict):
                        # Format 1: Direct dictionary with OHLC keys
                        if all(k in candle for k in ['open', 'high', 'low', 'close']):
                            df_data.append({
                                'open': float(candle['open']),
                                'high': float(candle['high']),
                                'low': float(candle['low']),
                                'close': float(candle['close']),
                                'time': candle.get('time', time.time())
                            })
                        # Format 2: Dictionary with different key names
                        elif all(k in candle for k in ['o', 'h', 'l', 'c']):
                            df_data.append({
                                'open': float(candle['o']),
                                'high': float(candle['h']),
                                'low': float(candle['l']),
                                'close': float(candle['c']),
                                'time': candle.get('t', candle.get('time', time.time()))
                            })
                    elif isinstance(candle, (list, tuple)) and len(candle) >= 4:
                        # Format 3: Array format [time, open, close, high, low] or similar
                        if len(candle) >= 5:
                            df_data.append({
                                'open': float(candle[1]),
                                'high': float(candle[3]),
                                'low': float(candle[4]),
                                'close': float(candle[2]),
                                'time': candle[0]
                            })
                        else:
                            # Assume [open, high, low, close]
                            df_data.append({
                                'open': float(candle[0]),
                                'high': float(candle[1]),
                                'low': float(candle[2]),
                                'close': float(candle[3]),
                                'time': time.time()
                            })
                except (ValueError, TypeError, IndexError) as e:
                    logger.debug(f"Skipping invalid candle data: {candle} - {str(e)}")
                    continue

            if len(df_data) < 100:  # Need minimum data for training
                logger.warning(f"⚠️ Insufficient data for {asset_name}: {len(df_data)} candles")
                return None

            # Create DataFrame
            data = pd.DataFrame(df_data)

            # Ensure proper data types
            for col in ['open', 'high', 'low', 'close']:
                data[col] = pd.to_numeric(data[col], errors='coerce')

            # Remove any rows with NaN values
            data = data.dropna()

            # Sort by time if time column exists
            if 'time' in data.columns:
                data = data.sort_values('time').reset_index(drop=True)
                # Convert time to datetime index
                data.index = pd.to_datetime(data['time'], unit='s')
                data = data.drop('time', axis=1)

            # Keep only OHLC columns
            data = data[['open', 'high', 'low', 'close']].copy()

            # Add metadata
            data.attrs['asset'] = asset_name
            data.attrs['symbol'] = symbol
            data.attrs['source'] = 'quotex_api'
            data.attrs['period'] = self.period
            data.attrs['fetch_time'] = datetime.now().isoformat()

            logger.info(f"✅ {asset_name}: {len(data)} data points fetched")
            return data

        except Exception as e:
            logger.error(f"❌ Error fetching {asset_name}: {str(e)}")
            return None

    async def disconnect_quotex(self):
        """
        Disconnect from Quotex API
        """
        try:
            if self.client:
                await self.client.close()
                logger.info("� Disconnected from Quotex API")
        except Exception as e:
            logger.error(f"❌ Error disconnecting from Quotex: {str(e)}")

    def validate_data(self, data, asset_name, min_points=1000):
        """
        Validate fetched data quality

        Args:
            data: DataFrame to validate
            asset_name: Asset name for logging
            min_points: Minimum required data points

        Returns:
            bool: True if data is valid
        """
        if data is None or data.empty:
            logger.warning(f"⚠️ {asset_name}: No data to validate")
            return False

        # Check minimum data points
        if len(data) < min_points:
            logger.warning(f"⚠️ {asset_name}: Only {len(data)} points (need {min_points})")
            return False

        # Check for required columns
        required_cols = ['open', 'high', 'low', 'close']
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            logger.warning(f"⚠️ {asset_name}: Missing columns: {missing_cols}")
            return False

        # Check for NaN values
        nan_count = data.isnull().sum().sum()
        if nan_count > 0:
            logger.warning(f"⚠️ {asset_name}: {nan_count} NaN values found")
            return False

        # Check price consistency (high >= low, etc.)
        invalid_prices = (data['high'] < data['low']).sum()
        if invalid_prices > 0:
            logger.warning(f"⚠️ {asset_name}: {invalid_prices} invalid price bars")
            return False

        logger.info(f"✅ {asset_name}: Data validation passed")
        return True

    def save_raw_data(self, data, asset_name):
        """
        Save raw data to CSV

        Args:
            data: DataFrame to save
            asset_name: Asset name for filename
        """
        try:
            filename = f"{asset_name}_raw_{self.days}days.csv"
            filepath = os.path.join(self.raw_data_dir, filename)

            # Save data
            data.to_csv(filepath, index=True)

            # Save metadata
            metadata = {
                'asset': asset_name,
                'symbol': data.attrs.get('symbol', ''),
                'source': data.attrs.get('source', ''),
                'interval': data.attrs.get('interval', ''),
                'fetch_time': data.attrs.get('fetch_time', ''),
                'data_points': len(data),
                'date_range': {
                    'start': data.index.min().isoformat(),
                    'end': data.index.max().isoformat()
                },
                'price_range': {
                    'min': float(data['low'].min()),
                    'max': float(data['high'].max())
                }
            }

            metadata_file = f"{asset_name}_raw_metadata.json"
            metadata_path = os.path.join(self.raw_data_dir, metadata_file)

            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)

            logger.info(f"💾 {asset_name}: Raw data saved to {filepath}")

        except Exception as e:
            logger.error(f"❌ Error saving raw data for {asset_name}: {str(e)}")

    def process_data(self, data, asset_name):
        """
        Process raw data for training

        Args:
            data: Raw DataFrame
            asset_name: Asset name

        Returns:
            Processed DataFrame
        """
        try:
            logger.info(f"🔧 Processing data for {asset_name}...")

            processed_data = data.copy()

            # 1. Calculate additional features
            processed_data['price_change'] = processed_data['close'].pct_change()
            processed_data['hl_ratio'] = (processed_data['high'] - processed_data['low']) / processed_data['close']
            processed_data['oc_ratio'] = (processed_data['close'] - processed_data['open']) / processed_data['open']

            # 2. Moving averages
            processed_data['sma_5'] = processed_data['close'].rolling(5).mean()
            processed_data['sma_10'] = processed_data['close'].rolling(10).mean()
            processed_data['sma_20'] = processed_data['close'].rolling(20).mean()

            # 3. Price position relative to moving averages
            processed_data['price_vs_sma5'] = processed_data['close'] / processed_data['sma_5']
            processed_data['price_vs_sma10'] = processed_data['close'] / processed_data['sma_10']
            processed_data['price_vs_sma20'] = processed_data['close'] / processed_data['sma_20']

            # 4. Volatility (rolling standard deviation)
            processed_data['volatility'] = processed_data['price_change'].rolling(20).std()

            # 5. Price labels for training (future price direction)
            processed_data['future_price'] = processed_data['close'].shift(-1)
            processed_data['price_direction'] = np.where(
                processed_data['future_price'] > processed_data['close'] * 1.001, 2,  # UP (>0.1%)
                np.where(processed_data['future_price'] < processed_data['close'] * 0.999, 0, 1)  # DOWN (<-0.1%), NEUTRAL
            )

            # 6. Remove NaN values
            processed_data = processed_data.dropna()

            # 7. Normalize OHLC data (for neural network)
            scaler = MinMaxScaler()
            ohlc_columns = ['open', 'high', 'low', 'close']
            processed_data[ohlc_columns] = scaler.fit_transform(processed_data[ohlc_columns])

            # Store scaler for later use
            processed_data.attrs = data.attrs.copy()
            processed_data.attrs['scaler'] = scaler
            processed_data.attrs['processed'] = True

            logger.info(f"✅ {asset_name}: Data processed - {len(processed_data)} points")
            return processed_data

        except Exception as e:
            logger.error(f"❌ Error processing data for {asset_name}: {str(e)}")
            return None

    def save_processed_data(self, data, asset_name):
        """
        Save processed data to CSV

        Args:
            data: Processed DataFrame to save
            asset_name: Asset name for filename
        """
        try:
            filename = f"{asset_name}_processed_{self.days}days.csv"
            filepath = os.path.join(self.processed_data_dir, filename)

            # Save data
            data.to_csv(filepath, index=True)

            # Save processing metadata
            metadata = {
                'asset': asset_name,
                'source': data.attrs.get('source', ''),
                'processed': True,
                'processing_time': datetime.now().isoformat(),
                'data_points': len(data),
                'features': list(data.columns),
                'label_distribution': {
                    'DOWN': int((data['price_direction'] == 0).sum()),
                    'NEUTRAL': int((data['price_direction'] == 1).sum()),
                    'UP': int((data['price_direction'] == 2).sum())
                },
                'date_range': {
                    'start': data.index.min().isoformat(),
                    'end': data.index.max().isoformat()
                }
            }

            metadata_file = f"{asset_name}_processed_metadata.json"
            metadata_path = os.path.join(self.processed_data_dir, metadata_file)

            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)

            logger.info(f"💾 {asset_name}: Processed data saved to {filepath}")

        except Exception as e:
            logger.error(f"❌ Error saving processed data for {asset_name}: {str(e)}")

    async def fetch_single_asset(self, asset_name, symbol):
        """
        Fetch and process data for a single asset

        Args:
            asset_name: Friendly asset name
            symbol: Trading symbol

        Returns:
            tuple: (raw_data, processed_data)
        """
        logger.info(f"🎯 Starting fetch for {asset_name}...")

        # Fetch data from Quotex API
        raw_data = await self.fetch_quotex_data(symbol, asset_name, self.days)

        if raw_data is None:
            logger.error(f"❌ Quotex fetch failed for {asset_name}")
            return None, None

        # Validate data
        if not self.validate_data(raw_data, asset_name):
            logger.error(f"❌ Data validation failed for {asset_name}")
            return None, None

        # Save raw data
        self.save_raw_data(raw_data, asset_name)

        # Process data
        processed_data = self.process_data(raw_data, asset_name)

        if processed_data is not None:
            # Save processed data
            self.save_processed_data(processed_data, asset_name)

        return raw_data, processed_data

    async def fetch_all_assets(self):
        """
        Fetch and process data for all configured assets

        Returns:
            dict: Results summary
        """
        logger.info("🚀 Starting data collection for all assets...")
        logger.info("=" * 60)

        # Connect to Quotex first
        if not await self.connect_quotex():
            logger.error("❌ Failed to connect to Quotex API")
            return None

        results = {
            'successful': [],
            'failed': [],
            'raw_data': {},
            'processed_data': {},
            'summary': {}
        }

        total_assets = len(self.assets)

        try:
            for i, (asset_name, symbol) in enumerate(self.assets.items(), 1):
                logger.info(f"\n📊 [{i}/{total_assets}] Processing {asset_name} ({symbol})...")

                try:
                    raw_data, processed_data = await self.fetch_single_asset(asset_name, symbol)

                    if raw_data is not None and processed_data is not None:
                        results['successful'].append(asset_name)
                        results['raw_data'][asset_name] = raw_data
                        results['processed_data'][asset_name] = processed_data

                        # Add to summary
                        results['summary'][asset_name] = {
                            'raw_points': len(raw_data),
                            'processed_points': len(processed_data),
                            'date_range': {
                                'start': raw_data.index.min().strftime('%Y-%m-%d %H:%M'),
                                'end': raw_data.index.max().strftime('%Y-%m-%d %H:%M')
                            },
                            'label_distribution': {
                                'DOWN': int((processed_data['price_direction'] == 0).sum()),
                                'NEUTRAL': int((processed_data['price_direction'] == 1).sum()),
                                'UP': int((processed_data['price_direction'] == 2).sum())
                            }
                        }

                        logger.info(f"✅ {asset_name}: Successfully processed")
                    else:
                        results['failed'].append(asset_name)
                        logger.error(f"❌ {asset_name}: Failed to process")

                except Exception as e:
                    results['failed'].append(asset_name)
                    logger.error(f"❌ {asset_name}: Exception occurred - {str(e)}")

                # Small delay to avoid rate limiting
                await asyncio.sleep(2)

        finally:
            # Always disconnect from Quotex
            await self.disconnect_quotex()

        # Print final summary
        self.print_summary(results)

        # Save overall summary
        self.save_summary(results)

        return results

    def print_summary(self, results):
        """
        Print collection summary

        Args:
            results: Results dictionary
        """
        logger.info("\n" + "=" * 60)
        logger.info("📊 DATA COLLECTION SUMMARY")
        logger.info("=" * 60)

        successful = len(results['successful'])
        failed = len(results['failed'])
        total = successful + failed

        logger.info(f"🎯 Total Assets: {total}")
        logger.info(f"✅ Successful: {successful} ({successful/total*100:.1f}%)")
        logger.info(f"❌ Failed: {failed} ({failed/total*100:.1f}%)")

        if results['successful']:
            logger.info(f"\n✅ Successful Assets:")
            for asset in results['successful']:
                summary = results['summary'][asset]
                logger.info(f"   📈 {asset}: {summary['raw_points']} raw, {summary['processed_points']} processed")

        if results['failed']:
            logger.info(f"\n❌ Failed Assets:")
            for asset in results['failed']:
                logger.info(f"   💥 {asset}")

        # Calculate total data points
        total_raw = sum(len(data) for data in results['raw_data'].values())
        total_processed = sum(len(data) for data in results['processed_data'].values())

        logger.info(f"\n📊 Total Data Points:")
        logger.info(f"   📈 Raw: {total_raw:,}")
        logger.info(f"   🔧 Processed: {total_processed:,}")

        logger.info("=" * 60)

    def save_summary(self, results):
        """
        Save collection summary to file

        Args:
            results: Results dictionary
        """
        try:
            summary_data = {
                'collection_time': datetime.now().isoformat(),
                'total_assets': len(self.assets),
                'successful_assets': len(results['successful']),
                'failed_assets': len(results['failed']),
                'successful_list': results['successful'],
                'failed_list': results['failed'],
                'asset_summaries': results['summary'],
                'configuration': {
                    'days': self.days,
                    'interval': self.interval,
                    'assets': self.assets
                }
            }

            summary_file = os.path.join(self.data_dir, "collection_summary.json")

            with open(summary_file, 'w') as f:
                json.dump(summary_data, f, indent=2)

            logger.info(f"💾 Collection summary saved to {summary_file}")

        except Exception as e:
            logger.error(f"❌ Error saving summary: {str(e)}")


async def main():
    """
    Main function to run the data collection
    """
    print("🚀 TRAINING DATA FETCHER FOR BRAIN MODEL")
    print("=" * 50)
    print("📊 Fetching 10 assets with 20 days of historical data")
    print("💾 Saving both raw and processed data per asset")
    print("🔗 Using Quotex API for real trading data")
    print("🎯 Ready for Google Colab training!")
    print("=" * 50)

    # Get Quotex credentials
    email = input("📧 Enter your Quotex email: ").strip()
    password = input("🔐 Enter your Quotex password: ").strip()

    if not email or not password:
        print("❌ Email and password are required!")
        return None

    # Initialize fetcher
    fetcher = TrainingDataFetcher(email=email, password=password)

    # Fetch all assets
    results = await fetcher.fetch_all_assets()

    if results:
        print("\n🎉 Data collection completed!")
        print(f"📁 Check the '{fetcher.data_dir}' directory for your training data")
        print("🚀 Ready to upload to Google Colab for training!")
    else:
        print("\n❌ Data collection failed!")

    return results


if __name__ == "__main__":
    asyncio.run(main())

# Advanced Trading Bot Requirements
# Install with: pip install -r requirements_advanced.txt

# Core dependencies (already included)
pandas>=1.5.0
numpy>=1.21.0
websockets>=10.0
aiohttp>=3.8.0
asyncio-mqtt>=0.11.0
python-dotenv>=0.19.0

# Advanced Technical Analysis Libraries
ta>=0.10.2
scipy>=1.9.0
scikit-learn>=1.1.0

# Optional: For enhanced performance
numba>=0.56.0
cython>=0.29.0

# GUI Dependencies (if using GUI)
PyQt5>=5.15.0
matplotlib>=3.5.0

# Development and Testing
pytest>=7.0.0
pytest-asyncio>=0.21.0

# Installation Notes:
#
# Easy Installation with TA library (recommended):
# pip install ta
#
# This is much easier than TA-Lib and provides the same functionality!
#
# For all dependencies at once:
# pip install -r requirements_advanced.txt
#
# Alternative installation without TA library:
# The bot will work without the TA library using manual calculations, but with reduced performance.
#
# The TA library is a pure Python implementation that's much easier to install than TA-Lib.

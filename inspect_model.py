"""
🔍 MODEL INSPECTOR
=================

Inspect the trained model to understand its input requirements.
"""

import tensorflow as tf
import numpy as np

def inspect_model():
    """Inspect the model structure and input requirements"""
    try:
        # Load the model
        model = tf.keras.models.load_model("models/brain_latest.h5")
        
        print("🔍 MODEL INSPECTION")
        print("=" * 50)
        
        # Model summary
        print("\n📊 Model Summary:")
        model.summary()
        
        # Input shape
        print(f"\n📐 Input Shape: {model.input_shape}")
        print(f"Expected input: (batch_size, {model.input_shape[1]}, {model.input_shape[2]})")
        print(f"Sequence Length: {model.input_shape[1]}")
        print(f"Number of Features: {model.input_shape[2]}")
        
        # Output shape
        print(f"\n📤 Output Shape: {model.output_shape}")
        print(f"Number of Classes: {model.output_shape[1]}")
        
        # Test with dummy data
        print(f"\n🧪 Testing with dummy data...")
        dummy_input = np.random.random((1, model.input_shape[1], model.input_shape[2]))
        prediction = model.predict(dummy_input, verbose=0)
        print(f"Dummy prediction shape: {prediction.shape}")
        print(f"Dummy prediction: {prediction[0]}")
        
        return model.input_shape[1], model.input_shape[2]  # sequence_length, num_features
        
    except Exception as e:
        print(f"❌ Error inspecting model: {e}")
        return None, None

if __name__ == "__main__":
    sequence_length, num_features = inspect_model()
    
    if sequence_length and num_features:
        print(f"\n✅ Model expects:")
        print(f"   - Sequence Length: {sequence_length}")
        print(f"   - Number of Features: {num_features}")
        print(f"\n💡 Update your prediction script to use exactly {num_features} features!")

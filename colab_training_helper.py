"""
🧠 GOOGLE COLAB TRAINING HELPER FOR BRAIN MODEL 🧠
==================================================

Helper functions for training the brain model in Google Colab
Loads the data fetched by fetch_training_data.py and prepares it for training

Features:
- Load raw and processed data from multiple assets
- Combine data for training
- Create sequences for LSTM
- Train and save the brain model
- Export model for use in trading bot

Author: Trading Bot AI
Version: 1.0.0 - Colab Training Helper
License: MIT
"""

import os
import pandas as pd
import numpy as np
import json
import pickle
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Machine Learning imports
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix

# TensorFlow imports
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.utils import to_categorical

# Visualization imports
import matplotlib.pyplot as plt
import seaborn as sns

class ColabTrainingHelper:
    """
    🧠 Google Colab Training Helper

    Helps train the brain model using data from fetch_training_data.py
    """

    def __init__(self, data_dir="training_data"):
        """
        Initialize the training helper

        Args:
            data_dir: Directory containing training data
        """
        self.data_dir = data_dir
        self.raw_data_dir = os.path.join(data_dir, "raw")
        self.processed_data_dir = os.path.join(data_dir, "processed")
        self.models_dir = os.path.join(data_dir, "models")

        # Create models directory
        os.makedirs(self.models_dir, exist_ok=True)

        self.sequence_length = 30
        self.prediction_horizon = 1

        print("🧠 ColabTrainingHelper initialized")
        print(f"📁 Data directory: {self.data_dir}")

    def load_collection_summary(self):
        """
        Load the data collection summary

        Returns:
            dict: Collection summary
        """
        try:
            summary_file = os.path.join(self.data_dir, "collection_summary.json")

            if not os.path.exists(summary_file):
                print("❌ Collection summary not found. Run fetch_training_data.py first!")
                return None

            with open(summary_file, 'r') as f:
                summary = json.load(f)

            print("📊 COLLECTION SUMMARY LOADED:")
            print(f"   🎯 Total Assets: {summary['total_assets']}")
            print(f"   ✅ Successful: {summary['successful_assets']}")
            print(f"   ❌ Failed: {summary['failed_assets']}")
            print(f"   📅 Collection Time: {summary['collection_time']}")

            return summary

        except Exception as e:
            print(f"❌ Error loading summary: {str(e)}")
            return None

    def load_processed_data(self, asset_list=None):
        """
        Load processed data for specified assets

        Args:
            asset_list: List of assets to load (None = load all)

        Returns:
            dict: Loaded data per asset
        """
        try:
            summary = self.load_collection_summary()
            if summary is None:
                return {}

            if asset_list is None:
                asset_list = summary['successful_list']

            loaded_data = {}

            print(f"📊 Loading processed data for {len(asset_list)} assets...")

            for asset in asset_list:
                try:
                    # Load processed data
                    filename = f"{asset}_processed_20days.csv"
                    filepath = os.path.join(self.processed_data_dir, filename)

                    if os.path.exists(filepath):
                        data = pd.read_csv(filepath, index_col=0, parse_dates=True)
                        loaded_data[asset] = data
                        print(f"   ✅ {asset}: {len(data)} data points loaded")
                    else:
                        print(f"   ❌ {asset}: File not found - {filepath}")

                except Exception as e:
                    print(f"   ❌ {asset}: Error loading - {str(e)}")

            print(f"📊 Successfully loaded {len(loaded_data)} assets")
            return loaded_data

        except Exception as e:
            print(f"❌ Error loading processed data: {str(e)}")
            return {}

    def create_sequences(self, data, sequence_length=30):
        """
        Create sequences for LSTM training

        Args:
            data: DataFrame with processed data
            sequence_length: Length of input sequences

        Returns:
            tuple: (X, y) arrays
        """
        try:
            # Use only OHLC columns for features (to match your model)
            feature_columns = ['open', 'high', 'low', 'close']
            features = data[feature_columns].values
            labels = data['price_direction'].values

            X, y = [], []

            for i in range(sequence_length, len(features)):
                # Input sequence
                X.append(features[i-sequence_length:i])
                # Output label
                y.append(labels[i])

            return np.array(X), np.array(y)

        except Exception as e:
            print(f"❌ Error creating sequences: {str(e)}")
            return None, None

    def prepare_training_data(self, asset_data):
        """
        Prepare combined training data from all assets

        Args:
            asset_data: Dictionary of asset DataFrames

        Returns:
            tuple: (X, y, label_counts)
        """
        try:
            print("🔧 Preparing training data...")

            all_X = []
            all_y = []

            for asset, data in asset_data.items():
                print(f"   📊 Processing {asset}...")

                X, y = self.create_sequences(data, self.sequence_length)

                if X is not None and len(X) > 0:
                    all_X.append(X)
                    all_y.append(y)

                    # Show label distribution for this asset
                    unique, counts = np.unique(y, return_counts=True)
                    label_dist = dict(zip(unique, counts))
                    print(f"      📈 {len(X)} sequences")
                    print(f"      📊 DOWN: {label_dist.get(0, 0)}, NEUTRAL: {label_dist.get(1, 0)}, UP: {label_dist.get(2, 0)}")
                else:
                    print(f"      ❌ No sequences created")

            if not all_X:
                print("❌ No training data could be prepared!")
                return None, None, None

            # Combine all data
            X_combined = np.vstack(all_X)
            y_combined = np.hstack(all_y)

            # Calculate overall label distribution
            unique, counts = np.unique(y_combined, return_counts=True)
            label_counts = dict(zip(unique, counts))
            total = len(y_combined)

            print(f"\n🎯 COMBINED TRAINING DATA:")
            print(f"   📊 Total sequences: {len(X_combined):,}")
            print(f"   📏 Input shape: {X_combined.shape}")
            print(f"   📈 Label distribution:")
            print(f"      DOWN (0): {label_counts.get(0, 0):,} ({label_counts.get(0, 0)/total*100:.1f}%)")
            print(f"      NEUTRAL (1): {label_counts.get(1, 0):,} ({label_counts.get(1, 0)/total*100:.1f}%)")
            print(f"      UP (2): {label_counts.get(2, 0):,} ({label_counts.get(2, 0)/total*100:.1f}%)")

            return X_combined, y_combined, label_counts

        except Exception as e:
            print(f"❌ Error preparing training data: {str(e)}")
            return None, None, None

    def split_data(self, X, y, train_size=0.8, val_size=0.1, test_size=0.1):
        """
        Split data into train, validation, and test sets

        Args:
            X: Features
            y: Labels
            train_size: Training data proportion
            val_size: Validation data proportion
            test_size: Test data proportion

        Returns:
            tuple: Split datasets
        """
        try:
            print("📊 Splitting data...")

            # Convert labels to categorical
            y_categorical = to_categorical(y, num_classes=3)

            # First split: separate test set
            X_temp, X_test, y_temp, y_test = train_test_split(
                X, y_categorical,
                test_size=test_size,
                random_state=42,
                stratify=y
            )

            # Second split: separate train and validation
            val_proportion = val_size / (train_size + val_size)
            X_train, X_val, y_train, y_val = train_test_split(
                X_temp, y_temp,
                test_size=val_proportion,
                random_state=42,
                stratify=np.argmax(y_temp, axis=1)
            )

            print(f"   🎯 Training: {len(X_train):,} samples ({len(X_train)/len(X)*100:.1f}%)")
            print(f"   🔍 Validation: {len(X_val):,} samples ({len(X_val)/len(X)*100:.1f}%)")
            print(f"   🧪 Test: {len(X_test):,} samples ({len(X_test)/len(X)*100:.1f}%)")

            return X_train, X_val, X_test, y_train, y_val, y_test

        except Exception as e:
            print(f"❌ Error splitting data: {str(e)}")
            return None, None, None, None, None, None

    def create_model(self, input_shape, num_classes=3):
        """
        Create the brain model architecture

        Args:
            input_shape: Shape of input data (sequence_length, features)
            num_classes: Number of output classes

        Returns:
            Compiled Keras model
        """
        try:
            print("🧠 Creating brain model architecture...")

            model = Sequential([
                # First LSTM layer
                LSTM(64, return_sequences=True, input_shape=input_shape),
                BatchNormalization(),
                Dropout(0.3),

                # Second LSTM layer
                LSTM(32, return_sequences=False),
                BatchNormalization(),
                Dropout(0.3),

                # Dense layers
                Dense(16, activation='relu'),
                Dropout(0.2),

                # Output layer
                Dense(num_classes, activation='softmax')
            ])

            # Compile model
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )

            print("✅ Model created successfully!")
            print(f"📊 Model parameters: {model.count_params():,}")

            return model

        except Exception as e:
            print(f"❌ Error creating model: {str(e)}")
            return None

    def train_model(self, model, X_train, y_train, X_val, y_val, epochs=100, batch_size=32):
        """
        Train the brain model

        Args:
            model: Keras model to train
            X_train, y_train: Training data
            X_val, y_val: Validation data
            epochs: Number of training epochs
            batch_size: Training batch size

        Returns:
            Training history
        """
        try:
            print("🚀 Starting model training...")

            # Create callbacks
            callbacks = [
                EarlyStopping(
                    monitor='val_loss',
                    patience=15,
                    restore_best_weights=True,
                    verbose=1
                ),
                ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=10,
                    min_lr=1e-7,
                    verbose=1
                )
            ]

            # Train model
            start_time = datetime.now()

            history = model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )

            end_time = datetime.now()
            training_duration = end_time - start_time

            print(f"\n🎉 Training completed!")
            print(f"⏰ Training duration: {training_duration}")
            print(f"📊 Total epochs: {len(history.history['loss'])}")

            return history

        except Exception as e:
            print(f"❌ Error training model: {str(e)}")
            return None

    def evaluate_model(self, model, X_test, y_test):
        """
        Evaluate the trained model

        Args:
            model: Trained Keras model
            X_test, y_test: Test data

        Returns:
            dict: Evaluation results
        """
        try:
            print("🧪 Evaluating model on test data...")

            # Make predictions
            y_pred = model.predict(X_test, verbose=0)
            y_pred_classes = np.argmax(y_pred, axis=1)
            y_true_classes = np.argmax(y_test, axis=1)

            # Calculate accuracy
            accuracy = (y_pred_classes == y_true_classes).mean()

            # Classification report
            class_names = ['DOWN', 'NEUTRAL', 'UP']
            report = classification_report(
                y_true_classes, y_pred_classes,
                target_names=class_names,
                output_dict=True
            )

            # Confusion matrix
            cm = confusion_matrix(y_true_classes, y_pred_classes)

            print(f"📊 Test Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
            print(f"\n📈 Classification Report:")
            print(classification_report(y_true_classes, y_pred_classes, target_names=class_names))

            return {
                'accuracy': accuracy,
                'classification_report': report,
                'confusion_matrix': cm,
                'predictions': y_pred,
                'true_labels': y_true_classes,
                'predicted_labels': y_pred_classes
            }

        except Exception as e:
            print(f"❌ Error evaluating model: {str(e)}")
            return None

    def plot_training_history(self, history):
        """
        Plot training history

        Args:
            history: Keras training history
        """
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))

            # Loss plot
            axes[0, 0].plot(history.history['loss'], label='Training Loss', color='blue')
            axes[0, 0].plot(history.history['val_loss'], label='Validation Loss', color='red')
            axes[0, 0].set_title('Model Loss')
            axes[0, 0].set_xlabel('Epoch')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].legend()
            axes[0, 0].grid(True)

            # Accuracy plot
            axes[0, 1].plot(history.history['accuracy'], label='Training Accuracy', color='blue')
            axes[0, 1].plot(history.history['val_accuracy'], label='Validation Accuracy', color='red')
            axes[0, 1].set_title('Model Accuracy')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('Accuracy')
            axes[0, 1].legend()
            axes[0, 1].grid(True)

            # Learning rate plot (if available)
            if 'lr' in history.history:
                axes[1, 0].plot(history.history['lr'], color='green')
                axes[1, 0].set_title('Learning Rate')
                axes[1, 0].set_xlabel('Epoch')
                axes[1, 0].set_ylabel('Learning Rate')
                axes[1, 0].set_yscale('log')
                axes[1, 0].grid(True)
            else:
                axes[1, 0].text(0.5, 0.5, 'Learning Rate\nNot Available',
                               ha='center', va='center', transform=axes[1, 0].transAxes)

            # Training summary
            final_loss = history.history['loss'][-1]
            final_val_loss = history.history['val_loss'][-1]
            final_acc = history.history['accuracy'][-1]
            final_val_acc = history.history['val_accuracy'][-1]

            summary_text = f"""Training Summary:
Final Training Loss: {final_loss:.4f}
Final Validation Loss: {final_val_loss:.4f}
Final Training Accuracy: {final_acc:.4f}
Final Validation Accuracy: {final_val_acc:.4f}
Total Epochs: {len(history.history['loss'])}"""

            axes[1, 1].text(0.1, 0.5, summary_text, transform=axes[1, 1].transAxes,
                           fontsize=12, verticalalignment='center')
            axes[1, 1].set_title('Training Summary')
            axes[1, 1].axis('off')

            plt.tight_layout()
            plt.show()

        except Exception as e:
            print(f"❌ Error plotting training history: {str(e)}")

    def plot_confusion_matrix(self, cm, class_names=['DOWN', 'NEUTRAL', 'UP']):
        """
        Plot confusion matrix

        Args:
            cm: Confusion matrix
            class_names: Class names for labels
        """
        try:
            plt.figure(figsize=(8, 6))
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=class_names, yticklabels=class_names)
            plt.title('Confusion Matrix')
            plt.xlabel('Predicted')
            plt.ylabel('Actual')
            plt.show()

        except Exception as e:
            print(f"❌ Error plotting confusion matrix: {str(e)}")

    def save_model(self, model, model_name="brain_latest"):
        """
        Save the trained model

        Args:
            model: Trained Keras model
            model_name: Name for the saved model

        Returns:
            str: Path to saved model
        """
        try:
            # Save as .h5 file (compatible with your bot)
            model_path = os.path.join(self.models_dir, f"{model_name}.h5")
            model.save(model_path)

            # Save model metadata
            metadata = {
                'model_name': model_name,
                'save_time': datetime.now().isoformat(),
                'input_shape': model.input_shape,
                'output_shape': model.output_shape,
                'total_params': model.count_params(),
                'architecture': 'LSTM',
                'sequence_length': self.sequence_length,
                'num_classes': 3,
                'class_names': ['DOWN', 'NEUTRAL', 'UP']
            }

            metadata_path = os.path.join(self.models_dir, f"{model_name}_metadata.json")
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)

            print(f"💾 Model saved to: {model_path}")
            print(f"📊 Metadata saved to: {metadata_path}")

            return model_path

        except Exception as e:
            print(f"❌ Error saving model: {str(e)}")
            return None

    def full_training_pipeline(self, asset_list=None):
        """
        Complete training pipeline

        Args:
            asset_list: List of assets to use (None = use all)

        Returns:
            dict: Training results
        """
        try:
            print("🚀 STARTING FULL TRAINING PIPELINE")
            print("=" * 50)

            # 1. Load data
            print("\n📊 Step 1: Loading data...")
            asset_data = self.load_processed_data(asset_list)

            if not asset_data:
                print("❌ No data loaded. Cannot proceed.")
                return None

            # 2. Prepare training data
            print("\n🔧 Step 2: Preparing training data...")
            X, y, label_counts = self.prepare_training_data(asset_data)

            if X is None:
                print("❌ Failed to prepare training data.")
                return None

            # 3. Split data
            print("\n📊 Step 3: Splitting data...")
            X_train, X_val, X_test, y_train, y_val, y_test = self.split_data(X, y)

            if X_train is None:
                print("❌ Failed to split data.")
                return None

            # 4. Create model
            print("\n🧠 Step 4: Creating model...")
            input_shape = (self.sequence_length, 4)  # 30 timesteps, 4 features (OHLC)
            model = self.create_model(input_shape)

            if model is None:
                print("❌ Failed to create model.")
                return None

            # 5. Train model
            print("\n🚀 Step 5: Training model...")
            history = self.train_model(model, X_train, y_train, X_val, y_val)

            if history is None:
                print("❌ Failed to train model.")
                return None

            # 6. Evaluate model
            print("\n🧪 Step 6: Evaluating model...")
            evaluation = self.evaluate_model(model, X_test, y_test)

            # 7. Plot results
            print("\n📊 Step 7: Plotting results...")
            self.plot_training_history(history)

            if evaluation:
                self.plot_confusion_matrix(evaluation['confusion_matrix'])

            # 8. Save model
            print("\n💾 Step 8: Saving model...")
            model_path = self.save_model(model)

            print("\n🎉 TRAINING PIPELINE COMPLETED!")
            print("=" * 50)

            results = {
                'model': model,
                'history': history,
                'evaluation': evaluation,
                'model_path': model_path,
                'data_summary': {
                    'total_sequences': len(X),
                    'training_sequences': len(X_train),
                    'validation_sequences': len(X_val),
                    'test_sequences': len(X_test),
                    'label_distribution': label_counts
                }
            }

            return results

        except Exception as e:
            print(f"❌ Error in training pipeline: {str(e)}")
            return None


# Convenience functions for Google Colab
def quick_train():
    """
    Quick training function for Google Colab
    """
    print("🚀 QUICK BRAIN MODEL TRAINING")
    print("=" * 40)

    # Initialize helper
    helper = ColabTrainingHelper()

    # Run full pipeline
    results = helper.full_training_pipeline()

    if results:
        print("\n✅ Training completed successfully!")
        print(f"📊 Final test accuracy: {results['evaluation']['accuracy']:.4f}")
        print(f"💾 Model saved to: {results['model_path']}")
        print("\n🎯 Ready to download and use in your trading bot!")
    else:
        print("\n❌ Training failed!")

    return results


if __name__ == "__main__":
    # Run quick training
    quick_train()

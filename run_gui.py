#!/usr/bin/env python3
"""
🚀 QUOTEX TRADING BOT GUI LAUNCHER 🚀

Simple launcher script for the PyQt5 GUI interface.
"""

import sys
import os

def check_requirements():
    """Check if all required packages are installed"""
    missing_packages = []
    
    try:
        import PyQt5
        print("✅ PyQt5 found")
    except ImportError:
        missing_packages.append("PyQt5")
    
    try:
        import pandas
        print("✅ Pandas found")
    except ImportError:
        missing_packages.append("pandas")
    
    try:
        import numpy
        print("✅ NumPy found")
    except ImportError:
        missing_packages.append("numpy")
    
    try:
        from bot import QuotexTradingBot
        print("✅ Trading bot found")
    except ImportError as e:
        print(f"❌ Trading bot import error: {e}")
        return False
    
    try:
        from model import IntelligentRuleBasedModel
        print("✅ Enhanced model found")
    except ImportError as e:
        print(f"❌ Model import error: {e}")
        return False
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with:")
        for package in missing_packages:
            print(f"   pip install {package}")
        return False
    
    return True

def main():
    """Main launcher function"""
    print("🎯 QUOTEX TRADING BOT GUI LAUNCHER")
    print("=" * 50)
    
    # Check requirements
    print("🔍 Checking requirements...")
    if not check_requirements():
        print("\n❌ Requirements check failed!")
        input("Press Enter to exit...")
        return
    
    print("\n✅ All requirements satisfied!")
    print("🚀 Launching GUI...")
    
    try:
        # Import and run the GUI
        from trading_bot_gui import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"❌ Failed to import GUI: {e}")
        print("Make sure trading_bot_gui.py is in the same directory")
        input("Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ GUI error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()

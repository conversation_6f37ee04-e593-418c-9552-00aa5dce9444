"""
🧠 QUOTEX AI PRICE ACTION TRAINER V2
===================================

Trains a regression model to predict price movements using OHLC data.
Matches the existing brain_latest.h5 model architecture.
"""

import os
import time
import json
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# ML Libraries
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    from sklearn.preprocessing import MinMaxScaler
    from sklearn.model_selection import train_test_split
    print("✅ TensorFlow and ML libraries loaded")
except ImportError as e:
    print(f"❌ Missing ML libraries: {e}")
    print("Install with: pip install tensorflow scikit-learn pandas numpy")
    exit(1)

# Quotex API
try:
    from quotexapi.stable_api import Quotex
    from quotexapi.expiration import get_timestamp_days_ago, timestamp_to_date
    from quotexapi.config import email, password
    print("✅ Quotex API loaded")
except ImportError as e:
    print(f"❌ Quotex API not found: {e}")
    exit(1)

# ============================================================================
# 🔧 CONFIGURATION
# ============================================================================

CONFIG = {
    "email": email,
    "password": password,
    "asset": "EURUSD_otc",
    "period": 60,  # 1-minute candles
    "days": 30,
    "sequence_length": 30,  # Match existing model
    "prediction_horizon": 1,  # Predict next 1 candle
    "model_path": "models/",
    "data_path": "data/",
    "features": ["open", "high", "low", "close"]  # Only OHLC features
}

# ============================================================================
# 📊 DATA FETCHER CLASS
# ============================================================================

class DataFetcher:

    def __init__(self):
        self.client = None

    async def connect(self, max_retries=3):
        """Connect to Quotex API with retry logic"""
        print("🔌 Connecting to Quotex...")

        for attempt in range(max_retries):
            try:
                print(f"Attempt {attempt + 1}/{max_retries}")

                self.client = Quotex(
                    email=CONFIG["email"],
                    password=CONFIG["password"],
                    lang="en"
                )

                self.client.debug_ws_enable = False

                check, reason = await self.client.connect()
                if check:
                    print("✅ Connected successfully!")
                    return True
                else:
                    print(f"❌ Connection failed: {reason}")
                    if attempt < max_retries - 1:
                        print("🔄 Retrying in 5 seconds...")
                        await asyncio.sleep(5)

            except Exception as e:
                print(f"❌ Connection error: {e}")
                if attempt < max_retries - 1:
                    print("🔄 Retrying in 5 seconds...")
                    await asyncio.sleep(5)
                else:
                    print("❌ All connection attempts failed!")
                    return False

        return False

    async def fetch_historical_data(self, asset, period, days):
        """Fetch historical data"""
        print(f"📈 Fetching {days} days data for {asset}...")

        all_candles = []
        offset = 3600  # 1 hour chunks
        size = days * 24

        start_timestamp = get_timestamp_days_ago(days)
        end_from_time = (int(start_timestamp) - int(start_timestamp) % period) + offset

        for i in range(size):
            try:
                print(f"🔄 Fetching chunk {i+1}/{size}", end="\r")

                candles = await self.client.get_candles(
                    asset=asset,
                    end_from_time=end_from_time,
                    offset=offset,
                    period=period,
                    progressive=True
                )

                if candles:
                    all_candles.extend(candles)

                end_from_time += offset
                await asyncio.sleep(0.05)  # Rate limiting

            except Exception as e:
                print(f"\n⚠️ Error in chunk {i+1}: {e}")
                continue

        # Remove duplicates and sort by time
        unique_candles = list({frozenset(d.items()): d for d in all_candles}.values())
        unique_candles.sort(key=lambda x: x.get('time', 0))

        print(f"\n✅ Fetched {len(unique_candles)} unique candles")
        return unique_candles

    def close(self):
        if self.client:
            self.client.close()

# ============================================================================
# 🤖 PRICE ACTION MODEL
# ============================================================================

class PriceActionModel:

    def __init__(self, sequence_length=30):
        self.sequence_length = sequence_length
        self.model = None
        self.scaler = MinMaxScaler()
        self.feature_columns = CONFIG["features"]

    def prepare_data(self, df):
        """Prepare data for LSTM training"""
        print("📊 Preparing data for training...")

        # Use only OHLC features
        features = df[self.feature_columns].values.astype(float)
        
        # Create target: next candle's price movement (regression)
        # Target is the normalized price change
        price_changes = df['close'].pct_change().shift(-1)  # Next candle's change
        
        # Convert to binary-like target (0-1 scale)
        # 0.5 = no change, >0.5 = up, <0.5 = down
        targets = (price_changes + 1) / 2  # Normalize to 0-1 range
        targets = np.clip(targets, 0.1, 0.9)  # Clip extreme values
        
        # Remove last row (no future target)
        features = features[:-1]
        targets = targets[:-1].values
        
        # Remove NaN values
        valid_indices = ~np.isnan(targets)
        features = features[valid_indices]
        targets = targets[valid_indices]

        # Scale features
        features_scaled = self.scaler.fit_transform(features)

        # Create sequences
        X_sequences, y_sequences = [], []

        for i in range(self.sequence_length, len(features_scaled)):
            X_sequences.append(features_scaled[i-self.sequence_length:i])
            y_sequences.append(targets[i])

        X_sequences = np.array(X_sequences)
        y_sequences = np.array(y_sequences)

        print(f"✅ Data shape: X={X_sequences.shape}, y={y_sequences.shape}")
        print(f"Target range: {y_sequences.min():.4f} to {y_sequences.max():.4f}")

        return X_sequences, y_sequences

    def build_model(self, input_shape):
        """Build LSTM regression model matching existing architecture"""
        print("🏗️ Building LSTM model...")

        model = Sequential([
            # First LSTM layer
            LSTM(64, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            
            # Second LSTM layer
            LSTM(64, return_sequences=False),
            Dropout(0.2),
            
            # Output layer (regression)
            Dense(1, activation='sigmoid')  # Sigmoid for 0-1 output
        ])

        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',  # Mean squared error for regression
            metrics=['mae']  # Mean absolute error
        )

        self.model = model
        print("✅ Model built successfully!")
        print(f"📊 Model expects input shape: {input_shape}")
        return model

    def train(self, X, y, validation_split=0.2, epochs=100):
        """Train the model"""
        print("🚀 Starting training...")

        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=validation_split, random_state=42
        )

        # Callbacks
        callbacks = [
            EarlyStopping(patience=15, restore_best_weights=True),
            ReduceLROnPlateau(patience=10, factor=0.5, min_lr=1e-7)
        ]

        # Train model
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )

        print("✅ Training completed!")
        return history

    def save_model(self, filepath):
        """Save model and metadata"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Save model
        self.model.save(filepath)

        # Save metadata
        metadata = {
            'scaler': self.scaler,
            'feature_columns': self.feature_columns,
            'sequence_length': self.sequence_length
        }

        import pickle
        with open(filepath.replace('.h5', '_metadata.pkl'), 'wb') as f:
            pickle.dump(metadata, f)

        print(f"💾 Model saved to {filepath}")

# ============================================================================
# 🎯 MAIN EXECUTION
# ============================================================================

async def main():
    """Main training pipeline"""
    print("🧠 QUOTEX AI PRICE ACTION TRAINER V2")
    print("=" * 50)

    # Create directories
    os.makedirs(CONFIG["model_path"], exist_ok=True)
    os.makedirs(CONFIG["data_path"], exist_ok=True)

    # Check existing files
    print("📁 Checking existing files...")
    raw_data_file = f"{CONFIG['data_path']}/raw_data_{CONFIG['asset']}.json"
    processed_data_file = f"{CONFIG['data_path']}/processed_data_{CONFIG['asset']}.csv"
    
    print(f"Raw data: {'✅ EXISTS' if os.path.exists(raw_data_file) else '❌ NOT FOUND'}")
    print(f"Processed data: {'✅ EXISTS' if os.path.exists(processed_data_file) else '❌ NOT FOUND'}")
    print()

    # 1. Load or fetch data
    if os.path.exists(raw_data_file):
        print(f"📁 Loading existing raw data...")
        with open(raw_data_file, 'r') as f:
            raw_data = json.load(f)
        print(f"✅ Loaded {len(raw_data)} existing candles")
    else:
        print("🔄 Fetching new data from API...")
        fetcher = DataFetcher()
        try:
            if not await fetcher.connect():
                return

            raw_data = await fetcher.fetch_historical_data(
                CONFIG["asset"],
                CONFIG["period"],
                CONFIG["days"]
            )

            if not raw_data:
                print("❌ No data fetched!")
                return

            # Save raw data
            print(f"💾 Saving raw data to {raw_data_file}")
            with open(raw_data_file, 'w') as f:
                json.dump(raw_data, f, indent=2)

        finally:
            fetcher.close()

    # 2. Process Data
    print("\n📊 Processing data...")
    
    # Handle different data formats
    if isinstance(raw_data, dict) and 'data' in raw_data:
        candle_data = raw_data['data']
    elif isinstance(raw_data, list):
        candle_data = raw_data
    else:
        print("❌ Unexpected data format!")
        return
    
    df = pd.DataFrame(candle_data)
    
    # Ensure numeric columns
    for col in CONFIG["features"]:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Remove rows with NaN values
    df = df.dropna(subset=CONFIG["features"])
    df = df.sort_values('time').reset_index(drop=True)

    print(f"Data shape: {df.shape}")
    print(f"Features: {CONFIG['features']}")

    # 3. Train Model
    model = PriceActionModel(CONFIG["sequence_length"])
    X, y = model.prepare_data(df)

    if len(X) == 0:
        print("❌ No valid training data!")
        return

    # Build and train model
    model.build_model((X.shape[1], X.shape[2]))
    history = model.train(X, y)

    # 4. Save Model
    model_filename = f"{CONFIG['model_path']}/brain_latest_v2.h5"
    model.save_model(model_filename)

    # 5. Training Summary
    print("\n🎉 TRAINING COMPLETED!")
    print("=" * 50)
    print(f"Asset: {CONFIG['asset']}")
    print(f"Timeframe: {CONFIG['period']}s")
    print(f"Data points: {len(df)}")
    print(f"Training samples: {len(X)}")
    print(f"Features: {CONFIG['features']}")
    print(f"Model saved: {model_filename}")

    final_loss = min(history.history['val_loss'])
    print(f"Best validation loss: {final_loss:.6f}")

if __name__ == "__main__":
    asyncio.run(main())

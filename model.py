"""
🧠 ENHANCED INTELLIGENT RULE-BASED TRADING MODEL 🧠
==================================================

Professional AI-Enhanced Price Action & Technical Analysis Engine
Advanced machine learning-inspired logic with institutional-grade analysis

Enhanced Features:
✅ Enhanced Price Action Analysis (Advanced S/R, Trend Analysis, Smart Breakouts)
✅ Professional Candle Psychology (Context-aware patterns, Volume confirmation)
✅ Advanced Market Structure Analysis (Elliott Wave, Institutional levels)
✅ Smart Supply & Demand Zone Detection (Order flow, Volume profile)
✅ Enhanced Momentum & Volatility Analysis (Multi-indicator confluence)
✅ AI-Powered Multi-timeframe Context (60s/90s optimized)
✅ Adaptive Learning System (Pattern performance tracking)
✅ Advanced Risk Management (Dynamic position sizing)
✅ Institutional Order Flow Analysis (Smart money detection)
✅ Enhanced Accuracy Tracking (Real-time performance metrics)

🆕 NEW ADVANCED FEATURES:
✅ Advanced Reversal Patterns (Hammer, Shooting Star, Inverted Hammer, Hanging Man, Spinning Top)
✅ Advanced Continuation Patterns (Maruboz<PERSON>, Belt Hold)
✅ Multi-Candle Patterns (Morning Star, Evening Star, Three Inside Up/Down)
✅ Advanced Price Patterns (Bull/Bear Flags, Triangles - Ascending, Descending, Symmetrical)
✅ Volume-Price Relationship Analysis
✅ Enhanced Pattern Priority System

Perfect for Binary Options Trading (1-2 minute expiry)
Author: Professional Quant Developer & AI Engineer
License: MIT
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timezone
import warnings
import json
import os
import time
import math
from collections import defaultdict, deque

# Advanced Technical Analysis Libraries
try:
    import ta
    TA_AVAILABLE = True
except ImportError:
    TA_AVAILABLE = False
    print("⚠️ TA library not available. Install with: pip install ta")

try:
    from scipy import stats
    from scipy.signal import find_peaks, argrelextrema
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("⚠️ SciPy not available. Install with: pip install scipy")

try:
    from sklearn.preprocessing import StandardScaler
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.linear_model import LinearRegression
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️ Scikit-learn not available. Install with: pip install scikit-learn")

warnings.filterwarnings('ignore')


class Signal(Enum):
    """Trading signal enumeration"""
    UP = "UP"
    DOWN = "DOWN"
    NEUTRAL = "NEUTRAL"


class TrendDirection(Enum):
    """Trend direction enumeration"""
    UPTREND = "UPTREND"
    DOWNTREND = "DOWNTREND"
    SIDEWAYS = "SIDEWAYS"
    UNKNOWN = "UNKNOWN"


class MarketSession(Enum):
    """Market session enumeration"""
    ASIAN = "ASIAN"
    LONDON = "LONDON"
    NEW_YORK = "NEW_YORK"
    OVERLAP_LONDON_NY = "OVERLAP_LONDON_NY"
    QUIET = "QUIET"


class CandleType(Enum):
    """Candle type enumeration"""
    BULLISH = "BULLISH"
    BEARISH = "BEARISH"
    DOJI = "DOJI"
    HAMMER = "HAMMER"
    SHOOTING_STAR = "SHOOTING_STAR"
    ENGULFING_BULL = "ENGULFING_BULL"
    ENGULFING_BEAR = "ENGULFING_BEAR"
    MARUBOZU_BULL = "MARUBOZU_BULL"
    MARUBOZU_BEAR = "MARUBOZU_BEAR"
    PIN_BAR_BULL = "PIN_BAR_BULL"
    PIN_BAR_BEAR = "PIN_BAR_BEAR"


@dataclass
class AnalysisResult:
    """Result from a single analysis component"""
    signal: Signal
    confidence: float
    weight: float = 1.0
    name: str = ""
    details: Dict = None


@dataclass
class PriceLevel:
    """Price level for support/resistance"""
    price: float
    strength: float
    touches: int
    level_type: str  # 'support' or 'resistance'


@dataclass
class MarketStructure:
    """Market structure analysis result"""
    trend: TrendDirection
    structure_points: List[Tuple[int, float, str]]  # (index, price, type)
    strength: float
    recent_shift: bool


@dataclass
class PatternPerformance:
    """Track performance of individual patterns"""
    pattern_name: str
    total_signals: int = 0
    successful_signals: int = 0
    total_confidence: float = 0.0
    avg_confidence: float = 0.0
    success_rate: float = 0.0
    last_updated: datetime = None

    def update_performance(self, was_successful: bool, confidence: float):
        """Update pattern performance metrics"""
        self.total_signals += 1
        self.total_confidence += confidence
        self.avg_confidence = self.total_confidence / self.total_signals

        if was_successful:
            self.successful_signals += 1

        self.success_rate = self.successful_signals / self.total_signals
        self.last_updated = datetime.now()


@dataclass
class TradingMemory:
    """Store trading history and outcomes for learning"""
    timestamp: datetime
    asset: str
    pattern_detected: str
    signal: str
    confidence: float
    outcome: Optional[str] = None  # 'win', 'loss', 'pending'
    market_session: str = 'unknown'
    market_volatility: float = 0.0

    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'asset': self.asset,
            'pattern_detected': self.pattern_detected,
            'signal': self.signal,
            'confidence': self.confidence,
            'outcome': self.outcome,
            'market_session': self.market_session,
            'market_volatility': self.market_volatility
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'TradingMemory':
        """Create from dictionary"""
        return cls(
            timestamp=datetime.fromisoformat(data['timestamp']),
            asset=data['asset'],
            pattern_detected=data['pattern_detected'],
            signal=data['signal'],
            confidence=data['confidence'],
            outcome=data.get('outcome'),
            market_session=data.get('market_session', 'unknown'),
            market_volatility=data.get('market_volatility', 0.0)
        )


class PriceActionAnalyzer:
    """🎯 Advanced Price Action Analysis Engine"""

    @staticmethod
    def find_swing_points(data: pd.DataFrame, lookback: int = 5) -> Tuple[List[Tuple[int, float]], List[Tuple[int, float]]]:
        """Find swing highs and lows for market structure analysis"""
        highs = data['high'].values
        lows = data['low'].values

        swing_highs = []
        swing_lows = []

        for i in range(lookback, len(data) - lookback):
            # Check for swing high
            is_swing_high = True
            for j in range(i - lookback, i + lookback + 1):
                if j != i and highs[j] >= highs[i]:
                    is_swing_high = False
                    break
            if is_swing_high:
                swing_highs.append((i, highs[i]))

            # Check for swing low
            is_swing_low = True
            for j in range(i - lookback, i + lookback + 1):
                if j != i and lows[j] <= lows[i]:
                    is_swing_low = False
                    break
            if is_swing_low:
                swing_lows.append((i, lows[i]))

        return swing_highs, swing_lows

    @staticmethod
    def analyze_market_structure(data: pd.DataFrame) -> MarketStructure:
        """Analyze market structure for trend direction and strength"""
        swing_highs, swing_lows = PriceActionAnalyzer.find_swing_points(data)

        if len(swing_highs) < 2 or len(swing_lows) < 2:
            return MarketStructure(TrendDirection.UNKNOWN, [], 0.0, False)

        # Analyze recent structure
        recent_highs = swing_highs[-3:] if len(swing_highs) >= 3 else swing_highs
        recent_lows = swing_lows[-3:] if len(swing_lows) >= 3 else swing_lows

        # Check for Higher Highs and Higher Lows (Uptrend)
        hh_count = 0
        hl_count = 0

        for i in range(1, len(recent_highs)):
            if recent_highs[i][1] > recent_highs[i-1][1]:
                hh_count += 1

        for i in range(1, len(recent_lows)):
            if recent_lows[i][1] > recent_lows[i-1][1]:
                hl_count += 1

        # Check for Lower Highs and Lower Lows (Downtrend)
        lh_count = 0
        ll_count = 0

        for i in range(1, len(recent_highs)):
            if recent_highs[i][1] < recent_highs[i-1][1]:
                lh_count += 1

        for i in range(1, len(recent_lows)):
            if recent_lows[i][1] < recent_lows[i-1][1]:
                ll_count += 1

        # Determine trend
        uptrend_strength = (hh_count + hl_count) / max(len(recent_highs) + len(recent_lows) - 2, 1)
        downtrend_strength = (lh_count + ll_count) / max(len(recent_highs) + len(recent_lows) - 2, 1)

        if uptrend_strength > 0.6:
            trend = TrendDirection.UPTREND
            strength = uptrend_strength
        elif downtrend_strength > 0.6:
            trend = TrendDirection.DOWNTREND
            strength = downtrend_strength
        else:
            trend = TrendDirection.SIDEWAYS
            strength = max(uptrend_strength, downtrend_strength)

        # Check for recent structure shift
        recent_shift = False
        if len(data) > 10:
            recent_price_action = data.tail(10)
            if len(swing_highs) > 0 and len(swing_lows) > 0:
                last_high = swing_highs[-1][1]
                last_low = swing_lows[-1][1]
                current_price = data['close'].iloc[-1]

                # Structure shift detection
                if trend == TrendDirection.UPTREND and current_price < last_low:
                    recent_shift = True
                elif trend == TrendDirection.DOWNTREND and current_price > last_high:
                    recent_shift = True

        structure_points = []
        for idx, price in swing_highs:
            structure_points.append((idx, price, 'high'))
        for idx, price in swing_lows:
            structure_points.append((idx, price, 'low'))

        return MarketStructure(trend, structure_points, strength, recent_shift)

    @staticmethod
    def find_support_resistance_levels(data: pd.DataFrame, min_touches: int = 2) -> List[PriceLevel]:
        """Find key support and resistance levels"""
        swing_highs, swing_lows = PriceActionAnalyzer.find_swing_points(data)

        levels = []
        tolerance = data['close'].std() * 0.5  # Dynamic tolerance based on volatility

        # Process swing highs for resistance
        high_prices = [price for _, price in swing_highs]
        for price in high_prices:
            touches = sum(1 for p in high_prices if abs(p - price) <= tolerance)
            if touches >= min_touches:
                strength = touches / len(high_prices)
                levels.append(PriceLevel(price, strength, touches, 'resistance'))

        # Process swing lows for support
        low_prices = [price for _, price in swing_lows]
        for price in low_prices:
            touches = sum(1 for p in low_prices if abs(p - price) <= tolerance)
            if touches >= min_touches:
                strength = touches / len(low_prices)
                levels.append(PriceLevel(price, strength, touches, 'support'))

        # Remove duplicate levels
        unique_levels = []
        for level in levels:
            is_duplicate = False
            for existing in unique_levels:
                if abs(level.price - existing.price) <= tolerance and level.level_type == existing.level_type:
                    if level.strength > existing.strength:
                        unique_levels.remove(existing)
                        unique_levels.append(level)
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_levels.append(level)

        return sorted(unique_levels, key=lambda x: x.strength, reverse=True)

    @staticmethod
    def detect_breakout_or_fakeout(data: pd.DataFrame, levels: List[PriceLevel]) -> Tuple[bool, str, float]:
        """Detect breakouts and fakeouts from key levels"""
        if len(data) < 5 or not levels:
            return False, "none", 0.0

        current_price = data['close'].iloc[-1]
        prev_prices = data['close'].tail(5).values

        for level in levels[:3]:  # Check top 3 strongest levels
            level_price = level.price
            tolerance = data['close'].std() * 0.3

            # Check if price recently broke through level
            broke_above = any(p <= level_price + tolerance for p in prev_prices[:-1]) and current_price > level_price + tolerance
            broke_below = any(p >= level_price - tolerance for p in prev_prices[:-1]) and current_price < level_price - tolerance

            if broke_above and level.level_type == 'resistance':
                # Breakout above resistance
                strength = min(0.9, (current_price - level_price) / level_price * 100)
                return True, "breakout_up", strength
            elif broke_below and level.level_type == 'support':
                # Breakdown below support
                strength = min(0.9, (level_price - current_price) / level_price * 100)
                return True, "breakout_down", strength

            # Check for fakeout (quick return to level)
            if broke_above and current_price <= level_price + tolerance:
                return True, "fakeout_down", 0.6
            elif broke_below and current_price >= level_price - tolerance:
                return True, "fakeout_up", 0.6

        return False, "none", 0.0

    @staticmethod
    def analyze_supply_demand_zones(data: pd.DataFrame) -> List[Dict]:
        """Identify basic supply and demand zones"""
        zones = []

        if len(data) < 20:
            return zones

        # Look for strong moves followed by consolidation
        for i in range(10, len(data) - 5):
            # Check for strong bullish move (demand zone)
            move_start = i - 5
            move_end = i
            consolidation_end = min(i + 5, len(data) - 1)

            move_range = data['high'].iloc[move_end] - data['low'].iloc[move_start]
            consolidation_range = data['high'].iloc[move_end:consolidation_end].max() - data['low'].iloc[move_end:consolidation_end].min()

            if move_range > 0 and consolidation_range > 0:
                move_strength = move_range / data['close'].iloc[move_start]
                consolidation_ratio = consolidation_range / move_range

                if move_strength > 0.01 and consolidation_ratio < 0.5:  # Strong move, tight consolidation
                    zone_type = "demand" if data['close'].iloc[move_end] > data['close'].iloc[move_start] else "supply"
                    zone_price = (data['high'].iloc[move_end:consolidation_end].max() + data['low'].iloc[move_end:consolidation_end].min()) / 2

                    zones.append({
                        'type': zone_type,
                        'price': zone_price,
                        'strength': move_strength,
                        'index': i
                    })

        return zones[-5:]  # Return last 5 zones

    @staticmethod
    def detect_advanced_price_patterns(data: pd.DataFrame) -> Dict[str, any]:
        """🎯 Detect advanced price action patterns (Flags, Pennants, Triangles, etc.)"""
        if len(data) < 20:
            return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

        # Get recent price action
        recent_data = data.tail(20)
        highs = recent_data['high']
        lows = recent_data['low']
        closes = recent_data['close']

        # Bull Flag Pattern
        bull_flag = PriceActionAnalyzer._detect_bull_flag(recent_data)
        if bull_flag['detected']:
            return bull_flag

        # Bear Flag Pattern
        bear_flag = PriceActionAnalyzer._detect_bear_flag(recent_data)
        if bear_flag['detected']:
            return bear_flag

        # Ascending Triangle
        asc_triangle = PriceActionAnalyzer._detect_ascending_triangle(recent_data)
        if asc_triangle['detected']:
            return asc_triangle

        # Descending Triangle
        desc_triangle = PriceActionAnalyzer._detect_descending_triangle(recent_data)
        if desc_triangle['detected']:
            return desc_triangle

        # Symmetrical Triangle
        sym_triangle = PriceActionAnalyzer._detect_symmetrical_triangle(recent_data)
        if sym_triangle['detected']:
            return sym_triangle

        return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

    @staticmethod
    def _detect_bull_flag(data: pd.DataFrame) -> Dict[str, any]:
        """Detect Bull Flag pattern"""
        if len(data) < 15:
            return {'detected': False}

        # Look for strong upward move followed by consolidation
        first_half = data.iloc[:10]
        second_half = data.iloc[10:]

        # Check for strong upward move in first half
        first_move = (first_half['close'].iloc[-1] - first_half['close'].iloc[0]) / first_half['close'].iloc[0]
        if first_move < 0.02:  # Less than 2% move
            return {'detected': False}

        # Check for consolidation in second half (flag)
        flag_high = second_half['high'].max()
        flag_low = second_half['low'].min()
        flag_range = (flag_high - flag_low) / flag_low

        if flag_range < 0.015:  # Tight consolidation (less than 1.5%)
            # Check if flag is sloping slightly down (typical bull flag)
            flag_slope = (second_half['close'].iloc[-1] - second_half['close'].iloc[0]) / len(second_half)

            return {
                'detected': True,
                'pattern': 'bull_flag',
                'strength': min(0.85, first_move * 20),  # Strength based on initial move
                'signal': 'up',
                'description': 'Bull Flag - Bullish continuation pattern'
            }

        return {'detected': False}

    @staticmethod
    def _detect_bear_flag(data: pd.DataFrame) -> Dict[str, any]:
        """Detect Bear Flag pattern"""
        if len(data) < 15:
            return {'detected': False}

        # Look for strong downward move followed by consolidation
        first_half = data.iloc[:10]
        second_half = data.iloc[10:]

        # Check for strong downward move in first half
        first_move = (first_half['close'].iloc[0] - first_half['close'].iloc[-1]) / first_half['close'].iloc[0]
        if first_move < 0.02:  # Less than 2% move
            return {'detected': False}

        # Check for consolidation in second half (flag)
        flag_high = second_half['high'].max()
        flag_low = second_half['low'].min()
        flag_range = (flag_high - flag_low) / flag_low

        if flag_range < 0.015:  # Tight consolidation
            return {
                'detected': True,
                'pattern': 'bear_flag',
                'strength': min(0.85, first_move * 20),
                'signal': 'down',
                'description': 'Bear Flag - Bearish continuation pattern'
            }

        return {'detected': False}

    @staticmethod
    def _detect_ascending_triangle(data: pd.DataFrame) -> Dict[str, any]:
        """Detect Ascending Triangle pattern"""
        if len(data) < 15:
            return {'detected': False}

        highs = data['high']
        lows = data['low']

        # Check for horizontal resistance (similar highs)
        recent_highs = highs.tail(10)
        high_variance = recent_highs.std() / recent_highs.mean()

        # Check for ascending support (higher lows)
        recent_lows = lows.tail(10)
        low_trend = recent_lows.diff().mean()

        if high_variance < 0.01 and low_trend > 0:  # Flat top, rising bottom
            return {
                'detected': True,
                'pattern': 'ascending_triangle',
                'strength': 0.75,
                'signal': 'up',
                'description': 'Ascending Triangle - Bullish breakout expected'
            }

        return {'detected': False}

    @staticmethod
    def _detect_descending_triangle(data: pd.DataFrame) -> Dict[str, any]:
        """Detect Descending Triangle pattern"""
        if len(data) < 15:
            return {'detected': False}

        highs = data['high']
        lows = data['low']

        # Check for horizontal support (similar lows)
        recent_lows = lows.tail(10)
        low_variance = recent_lows.std() / recent_lows.mean()

        # Check for descending resistance (lower highs)
        recent_highs = highs.tail(10)
        high_trend = recent_highs.diff().mean()

        if low_variance < 0.01 and high_trend < 0:  # Flat bottom, falling top
            return {
                'detected': True,
                'pattern': 'descending_triangle',
                'strength': 0.75,
                'signal': 'down',
                'description': 'Descending Triangle - Bearish breakout expected'
            }

        return {'detected': False}

    @staticmethod
    def _detect_symmetrical_triangle(data: pd.DataFrame) -> Dict[str, any]:
        """Detect Symmetrical Triangle pattern"""
        if len(data) < 15:
            return {'detected': False}

        highs = data['high']
        lows = data['low']

        # Check for converging trend lines
        recent_highs = highs.tail(10)
        recent_lows = lows.tail(10)

        high_trend = recent_highs.diff().mean()
        low_trend = recent_lows.diff().mean()

        # Highs should be declining, lows should be rising
        if high_trend < -0.001 and low_trend > 0.001:
            # Check if they're converging
            range_start = recent_highs.iloc[0] - recent_lows.iloc[0]
            range_end = recent_highs.iloc[-1] - recent_lows.iloc[-1]

            if range_end < range_start * 0.7:  # Range has contracted
                return {
                    'detected': True,
                    'pattern': 'symmetrical_triangle',
                    'strength': 0.7,
                    'signal': 'neutral',  # Direction depends on breakout
                    'description': 'Symmetrical Triangle - Breakout direction uncertain'
                }

        return {'detected': False}

    @staticmethod
    def analyze_volume_price_relationship(data: pd.DataFrame) -> Dict[str, any]:
        """📊 Analyze Volume-Price Relationship (if volume data available)"""
        # Note: Most forex/crypto APIs don't provide volume, but this is for completeness
        if 'volume' not in data.columns:
            return {'analysis': 'no_volume_data', 'strength': 0.0}

        if len(data) < 10:
            return {'analysis': 'insufficient_data', 'strength': 0.0}

        recent_data = data.tail(10)
        price_change = recent_data['close'].pct_change()
        volume_change = recent_data['volume'].pct_change()

        # Calculate correlation between price and volume changes
        correlation = price_change.corr(volume_change)

        if correlation > 0.5:
            return {
                'analysis': 'healthy_uptrend',
                'strength': min(0.8, correlation),
                'signal': 'up',
                'description': 'Volume confirms price movement'
            }
        elif correlation < -0.5:
            return {
                'analysis': 'volume_divergence',
                'strength': min(0.8, abs(correlation)),
                'signal': 'down',
                'description': 'Volume divergence suggests weakness'
            }
        else:
            return {
                'analysis': 'neutral_volume',
                'strength': 0.3,
                'signal': 'neutral',
                'description': 'Volume provides no clear signal'
            }


class AdvancedMathematicalIndicators:
    """🧮 ADVANCED MATHEMATICAL INDICATORS FOR SUPERIOR ACCURACY 🧮"""

    @staticmethod
    def calculate_kama(data: pd.DataFrame, period: int = 14, fast_sc: float = 2.0, slow_sc: float = 30.0) -> pd.Series:
        """Calculate Kaufman's Adaptive Moving Average - Self-adjusting to market volatility"""
        try:
            close = data['close']

            # Calculate efficiency ratio
            change = abs(close - close.shift(period))
            volatility = abs(close.diff()).rolling(window=period).sum()
            efficiency_ratio = change / volatility

            # Calculate smoothing constants
            fast_alpha = 2.0 / (fast_sc + 1.0)
            slow_alpha = 2.0 / (slow_sc + 1.0)
            sc = (efficiency_ratio * (fast_alpha - slow_alpha) + slow_alpha) ** 2

            # Calculate KAMA
            kama = pd.Series(index=close.index, dtype=float)
            kama.iloc[period] = close.iloc[period]

            for i in range(period + 1, len(close)):
                kama.iloc[i] = kama.iloc[i-1] + sc.iloc[i] * (close.iloc[i] - kama.iloc[i-1])

            return kama
        except:
            return pd.Series(index=data.index, dtype=float)

    @staticmethod
    def calculate_zlema(data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Zero Lag Exponential Moving Average - Eliminates moving average lag"""
        try:
            close = data['close']
            lag = int((period - 1) / 2)

            # Calculate zero lag data
            zero_lag_data = close + (close - close.shift(lag))

            # Apply EMA to zero lag data
            return zero_lag_data.ewm(span=period).mean()
        except:
            return pd.Series(index=data.index, dtype=float)

    @staticmethod
    def calculate_frama(data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Fractal Adaptive Moving Average - Adapts to market fractality"""
        try:
            close = data['close']
            high = data['high']
            low = data['low']

            frama = pd.Series(index=close.index, dtype=float)
            frama.iloc[0] = close.iloc[0]

            for i in range(1, len(close)):
                if i < period:
                    frama.iloc[i] = close.iloc[i]
                    continue

                # Calculate fractal dimension
                n1 = period // 2
                n2 = period

                hh1 = high.iloc[i-n1:i+1].max()
                ll1 = low.iloc[i-n1:i+1].min()
                hh2 = high.iloc[i-n2:i+1].max()
                ll2 = low.iloc[i-n2:i+1].min()

                if hh1 - ll1 > 0 and hh2 - ll2 > 0:
                    d1 = (hh1 - ll1) / n1
                    d2 = (hh2 - ll2) / n2

                    if d1 > 0 and d2 > 0:
                        fractal_dim = (np.log(d1) - np.log(d2)) / np.log(2)
                        alpha = np.exp(-4.6 * (fractal_dim - 1))
                        alpha = max(0.01, min(1.0, alpha))
                    else:
                        alpha = 0.5
                else:
                    alpha = 0.5

                frama.iloc[i] = alpha * close.iloc[i] + (1 - alpha) * frama.iloc[i-1]

            return frama
        except:
            return pd.Series(index=data.index, dtype=float)

    @staticmethod
    def calculate_jurik_ma(data: pd.DataFrame, period: int = 14, phase: float = 0) -> pd.Series:
        """Calculate Jurik Moving Average - Superior smoothing with minimal lag"""
        try:
            close = data['close']

            # Simplified Jurik MA calculation
            beta = 0.45 * (period - 1) / (0.45 * (period - 1) + 2)
            alpha = beta

            jma = pd.Series(index=close.index, dtype=float)
            e0 = pd.Series(index=close.index, dtype=float)
            e1 = pd.Series(index=close.index, dtype=float)
            e2 = pd.Series(index=close.index, dtype=float)

            jma.iloc[0] = close.iloc[0]
            e0.iloc[0] = close.iloc[0]
            e1.iloc[0] = close.iloc[0]
            e2.iloc[0] = close.iloc[0]

            for i in range(1, len(close)):
                e0.iloc[i] = (1 - alpha) * close.iloc[i] + alpha * e0.iloc[i-1]
                e1.iloc[i] = (close.iloc[i] - e0.iloc[i]) * (1 - beta) + beta * e1.iloc[i-1]
                e2.iloc[i] = (e0.iloc[i] + phase * e1.iloc[i] - jma.iloc[i-1]) * alpha**2 + (1 - alpha**2) * e2.iloc[i-1]
                jma.iloc[i] = e2.iloc[i] + jma.iloc[i-1]

            return jma
        except:
            return pd.Series(index=data.index, dtype=float)


class AdvancedTechnicalAnalyzer:
    """🚀 ADVANCED TECHNICAL ANALYSIS ENGINE 🚀

    Implements sophisticated technical indicators and analysis methods
    for maximum accuracy in binary options trading.
    """

    @staticmethod
    def calculate_advanced_rsi(data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate RSI using TA library or manual calculation"""
        if TA_AVAILABLE:
            return ta.momentum.RSIIndicator(data['close'], window=period).rsi()
        else:
            # Manual RSI calculation with smoothing
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

    @staticmethod
    def calculate_stochastic_rsi(data: pd.DataFrame, period: int = 14, k_period: int = 3, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """Calculate Stochastic RSI for precise overbought/oversold levels"""
        if TA_AVAILABLE:
            stoch_rsi = ta.momentum.StochRSIIndicator(data['close'], window=period,
                                                    smooth1=k_period, smooth2=d_period)
            return stoch_rsi.stochrsi_k(), stoch_rsi.stochrsi_d()
        else:
            # Manual calculation
            rsi = AdvancedTechnicalAnalyzer.calculate_advanced_rsi(data, period)
            rsi_min = rsi.rolling(window=period).min()
            rsi_max = rsi.rolling(window=period).max()
            stoch_rsi = (rsi - rsi_min) / (rsi_max - rsi_min) * 100
            k = stoch_rsi.rolling(window=k_period).mean()
            d = k.rolling(window=d_period).mean()
            return k, d

    @staticmethod
    def calculate_bollinger_bands(data: pd.DataFrame, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands with custom parameters"""
        if TA_AVAILABLE:
            bb = ta.volatility.BollingerBands(data['close'], window=period, window_dev=std_dev)
            return bb.bollinger_hband(), bb.bollinger_mavg(), bb.bollinger_lband()
        else:
            # Manual calculation
            sma = data['close'].rolling(window=period).mean()
            std = data['close'].rolling(window=period).std()
            upper = sma + (std * std_dev)
            lower = sma - (std * std_dev)
            return upper, sma, lower

    @staticmethod
    def calculate_macd_advanced(data: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD with histogram"""
        if TA_AVAILABLE:
            macd_indicator = ta.trend.MACD(data['close'], window_fast=fast, window_slow=slow, window_sign=signal)
            return macd_indicator.macd(), macd_indicator.macd_signal(), macd_indicator.macd_diff()
        else:
            # Manual calculation
            ema_fast = data['close'].ewm(span=fast).mean()
            ema_slow = data['close'].ewm(span=slow).mean()
            macd = ema_fast - ema_slow
            signal_line = macd.ewm(span=signal).mean()
            histogram = macd - signal_line
            return macd, signal_line, histogram

    @staticmethod
    def calculate_williams_r(data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Williams %R for momentum analysis"""
        if TA_AVAILABLE:
            return ta.momentum.WilliamsRIndicator(data['high'], data['low'], data['close'], lbp=period).williams_r()
        else:
            # Manual calculation
            highest_high = data['high'].rolling(window=period).max()
            lowest_low = data['low'].rolling(window=period).min()
            return -100 * (highest_high - data['close']) / (highest_high - lowest_low)

    @staticmethod
    def calculate_cci(data: pd.DataFrame, period: int = 20) -> pd.Series:
        """Calculate Commodity Channel Index"""
        if TA_AVAILABLE:
            return ta.trend.CCIIndicator(data['high'], data['low'], data['close'], window=period).cci()
        else:
            # Manual calculation
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            sma_tp = typical_price.rolling(window=period).mean()
            mad = typical_price.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
            return (typical_price - sma_tp) / (0.015 * mad)

    @staticmethod
    def calculate_adx(data: pd.DataFrame, period: int = 14) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate ADX, +DI, -DI for trend strength"""
        if TA_AVAILABLE:
            adx_indicator = ta.trend.ADXIndicator(data['high'], data['low'], data['close'], window=period)
            return adx_indicator.adx(), adx_indicator.adx_pos(), adx_indicator.adx_neg()
        else:
            # Manual calculation (simplified)
            high_diff = data['high'].diff()
            low_diff = data['low'].diff()

            plus_dm = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
            minus_dm = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)

            tr = np.maximum(data['high'] - data['low'],
                           np.maximum(abs(data['high'] - data['close'].shift(1)),
                                    abs(data['low'] - data['close'].shift(1))))

            atr = pd.Series(tr).rolling(window=period).mean()
            plus_di = 100 * pd.Series(plus_dm).rolling(window=period).mean() / atr
            minus_di = 100 * pd.Series(minus_dm).rolling(window=period).mean() / atr

            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            adx = dx.rolling(window=period).mean()

            return adx, plus_di, minus_di

    @staticmethod
    def detect_divergences(data: pd.DataFrame, indicator: pd.Series, lookback: int = 5) -> Dict[str, any]:
        """Detect bullish and bearish divergences between price and indicator"""
        if len(data) < lookback * 2:
            return {'bullish_divergence': False, 'bearish_divergence': False, 'strength': 0.0}

        # Find recent peaks and troughs in price
        price_peaks = []
        price_troughs = []
        indicator_peaks = []
        indicator_troughs = []

        for i in range(lookback, len(data) - lookback):
            # Check for price peaks
            if all(data['high'].iloc[i] >= data['high'].iloc[j] for j in range(i-lookback, i+lookback+1) if j != i):
                price_peaks.append((i, data['high'].iloc[i]))
                indicator_peaks.append((i, indicator.iloc[i]))

            # Check for price troughs
            if all(data['low'].iloc[i] <= data['low'].iloc[j] for j in range(i-lookback, i+lookback+1) if j != i):
                price_troughs.append((i, data['low'].iloc[i]))
                indicator_troughs.append((i, indicator.iloc[i]))

        bullish_divergence = False
        bearish_divergence = False

        # Check for bullish divergence (lower lows in price, higher lows in indicator)
        if len(price_troughs) >= 2 and len(indicator_troughs) >= 2:
            recent_price_trough = price_troughs[-1][1]
            prev_price_trough = price_troughs[-2][1]
            recent_indicator_trough = indicator_troughs[-1][1]
            prev_indicator_trough = indicator_troughs[-2][1]

            if recent_price_trough < prev_price_trough and recent_indicator_trough > prev_indicator_trough:
                bullish_divergence = True

        # Check for bearish divergence (higher highs in price, lower highs in indicator)
        if len(price_peaks) >= 2 and len(indicator_peaks) >= 2:
            recent_price_peak = price_peaks[-1][1]
            prev_price_peak = price_peaks[-2][1]
            recent_indicator_peak = indicator_peaks[-1][1]
            prev_indicator_peak = indicator_peaks[-2][1]

            if recent_price_peak > prev_price_peak and recent_indicator_peak < prev_indicator_peak:
                bearish_divergence = True

        return {
            'bullish_divergence': bullish_divergence,
            'bearish_divergence': bearish_divergence,
            'strength': 0.8 if bullish_divergence or bearish_divergence else 0.0
        }


class AdaptiveLearningEngine:
    """🧠 AI-LIKE ADAPTIVE LEARNING SYSTEM 🧠"""

    def __init__(self, memory_file: str = "trading_memory.json", max_memory_size: int = 10000):
        self.memory_file = memory_file
        self.max_memory_size = max_memory_size
        self.trading_memories: deque = deque(maxlen=max_memory_size)
        self.pattern_performance: Dict[str, PatternPerformance] = {}
        self.asset_performance: Dict[str, Dict] = defaultdict(lambda: {
            'total_trades': 0,
            'successful_trades': 0,
            'success_rate': 0.0,
            'avg_confidence': 0.0,
            'best_patterns': []
        })
        self.session_performance: Dict[str, Dict] = defaultdict(lambda: {
            'total_trades': 0,
            'successful_trades': 0,
            'success_rate': 0.0
        })

        # Dynamic confidence adjustments
        self.confidence_adjustments: Dict[str, float] = defaultdict(float)
        self.pattern_weights: Dict[str, float] = defaultdict(lambda: 1.0)

        # Load existing memory
        self.load_memory()

    def add_trading_memory(self, memory: TradingMemory):
        """Add new trading memory and update learning"""
        self.trading_memories.append(memory)

        # Update pattern performance if outcome is known
        if memory.outcome in ['win', 'loss']:
            self._update_pattern_performance(memory)
            self._update_asset_performance(memory)
            self._update_session_performance(memory)
            self._recalculate_dynamic_weights()

        # Save memory periodically
        if len(self.trading_memories) % 10 == 0:
            self.save_memory()

    def _update_pattern_performance(self, memory: TradingMemory):
        """Update performance metrics for detected pattern"""
        pattern_name = memory.pattern_detected

        if pattern_name not in self.pattern_performance:
            self.pattern_performance[pattern_name] = PatternPerformance(pattern_name)

        was_successful = memory.outcome == 'win'
        self.pattern_performance[pattern_name].update_performance(was_successful, memory.confidence)

    def _update_asset_performance(self, memory: TradingMemory):
        """Update performance metrics for specific asset"""
        asset = memory.asset
        perf = self.asset_performance[asset]

        perf['total_trades'] += 1
        if memory.outcome == 'win':
            perf['successful_trades'] += 1

        perf['success_rate'] = perf['successful_trades'] / perf['total_trades']

        # Update average confidence
        total_conf = perf.get('total_confidence', 0.0) + memory.confidence
        perf['total_confidence'] = total_conf
        perf['avg_confidence'] = total_conf / perf['total_trades']

    def _update_session_performance(self, memory: TradingMemory):
        """Update performance metrics for market session"""
        session = memory.market_session
        perf = self.session_performance[session]

        perf['total_trades'] += 1
        if memory.outcome == 'win':
            perf['successful_trades'] += 1

        perf['success_rate'] = perf['successful_trades'] / perf['total_trades']

    def _recalculate_dynamic_weights(self):
        """Recalculate pattern weights based on recent performance"""
        for pattern_name, performance in self.pattern_performance.items():
            if performance.total_signals >= 5:  # Minimum sample size
                # Adjust weight based on success rate
                base_weight = 1.0
                success_factor = performance.success_rate
                confidence_factor = min(performance.avg_confidence, 1.0)

                # Boost successful patterns, reduce unsuccessful ones
                if success_factor > 0.6:
                    weight_multiplier = 1.0 + (success_factor - 0.6) * 2.0  # Up to 1.8x
                elif success_factor < 0.4:
                    weight_multiplier = 0.5 + success_factor  # Down to 0.9x
                else:
                    weight_multiplier = 1.0

                self.pattern_weights[pattern_name] = base_weight * weight_multiplier * confidence_factor

    def get_adaptive_confidence(self, pattern_name: str, base_confidence: float,
                              asset: str, market_session: str) -> float:
        """Get AI-adjusted confidence based on learning"""
        adjusted_confidence = base_confidence

        # Pattern-based adjustment
        if pattern_name in self.pattern_performance:
            perf = self.pattern_performance[pattern_name]
            if perf.total_signals >= 3:  # Minimum sample size
                pattern_factor = perf.success_rate
                adjusted_confidence *= (0.7 + pattern_factor * 0.6)  # 0.7 to 1.3 multiplier

        # Asset-based adjustment
        if asset in self.asset_performance:
            asset_perf = self.asset_performance[asset]
            if asset_perf['total_trades'] >= 3:
                asset_factor = asset_perf['success_rate']
                adjusted_confidence *= (0.8 + asset_factor * 0.4)  # 0.8 to 1.2 multiplier

        # Session-based adjustment
        if market_session in self.session_performance:
            session_perf = self.session_performance[market_session]
            if session_perf['total_trades'] >= 3:
                session_factor = session_perf['success_rate']
                adjusted_confidence *= (0.9 + session_factor * 0.2)  # 0.9 to 1.1 multiplier

        # Ensure confidence stays within bounds
        return max(0.1, min(0.95, adjusted_confidence))

    def get_pattern_weight(self, pattern_name: str) -> float:
        """Get dynamic weight for pattern based on learning"""
        return self.pattern_weights.get(pattern_name, 1.0)

    def should_trade_asset(self, asset: str) -> Tuple[bool, float]:
        """AI decision on whether to trade specific asset"""
        if asset not in self.asset_performance:
            return True, 1.0  # No data, allow trading

        perf = self.asset_performance[asset]
        if perf['total_trades'] < 5:
            return True, 1.0  # Insufficient data

        success_rate = perf['success_rate']

        # Avoid assets with consistently poor performance
        if success_rate < 0.3 and perf['total_trades'] >= 10:
            return False, 0.0

        # Reduce confidence for underperforming assets
        confidence_multiplier = max(0.5, success_rate * 1.5)
        return True, confidence_multiplier

    def get_learning_insights(self) -> Dict[str, any]:
        """Get insights from learning process"""
        insights = {
            'total_memories': len(self.trading_memories),
            'pattern_insights': {},
            'asset_insights': {},
            'session_insights': {},
            'top_patterns': [],
            'worst_patterns': []
        }

        # Pattern insights
        for name, perf in self.pattern_performance.items():
            if perf.total_signals >= 3:
                insights['pattern_insights'][name] = {
                    'success_rate': round(perf.success_rate, 3),
                    'avg_confidence': round(perf.avg_confidence, 3),
                    'total_signals': perf.total_signals,
                    'weight': round(self.pattern_weights[name], 3)
                }

        # Top and worst patterns
        sorted_patterns = sorted(
            [(name, perf.success_rate) for name, perf in self.pattern_performance.items()
             if perf.total_signals >= 3],
            key=lambda x: x[1], reverse=True
        )

        insights['top_patterns'] = sorted_patterns[:5]
        insights['worst_patterns'] = sorted_patterns[-3:]

        return insights

    def save_memory(self):
        """Save trading memories to file"""
        try:
            data = {
                'memories': [memory.to_dict() for memory in self.trading_memories],
                'pattern_performance': {
                    name: {
                        'pattern_name': perf.pattern_name,
                        'total_signals': perf.total_signals,
                        'successful_signals': perf.successful_signals,
                        'total_confidence': perf.total_confidence,
                        'avg_confidence': perf.avg_confidence,
                        'success_rate': perf.success_rate,
                        'last_updated': perf.last_updated.isoformat() if perf.last_updated else None
                    }
                    for name, perf in self.pattern_performance.items()
                },
                'pattern_weights': dict(self.pattern_weights),
                'asset_performance': dict(self.asset_performance),
                'session_performance': dict(self.session_performance)
            }

            with open(self.memory_file, 'w') as f:
                json.dump(data, f, indent=2)

        except Exception as e:
            print(f"Error saving memory: {e}")

    def load_memory(self):
        """Load trading memories from file"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r') as f:
                    data = json.load(f)

                # Load memories
                for memory_data in data.get('memories', []):
                    memory = TradingMemory.from_dict(memory_data)
                    self.trading_memories.append(memory)

                # Load pattern performance
                for name, perf_data in data.get('pattern_performance', {}).items():
                    perf = PatternPerformance(name)
                    perf.total_signals = perf_data['total_signals']
                    perf.successful_signals = perf_data['successful_signals']
                    perf.total_confidence = perf_data['total_confidence']
                    perf.avg_confidence = perf_data['avg_confidence']
                    perf.success_rate = perf_data['success_rate']
                    if perf_data['last_updated']:
                        perf.last_updated = datetime.fromisoformat(perf_data['last_updated'])
                    self.pattern_performance[name] = perf

                # Load weights and performance data
                self.pattern_weights.update(data.get('pattern_weights', {}))
                self.asset_performance.update(data.get('asset_performance', {}))
                self.session_performance.update(data.get('session_performance', {}))

        except Exception as e:
            print(f"Error loading memory: {e}")


class MarketSessionAnalyzer:
    """🌍 Market Session Awareness & Time-Based Analysis Engine"""

    @staticmethod
    def get_current_market_session() -> MarketSession:
        """Determine current market session based on UTC time"""
        try:
            # Get current UTC time
            utc_now = datetime.now(timezone.utc)
            hour = utc_now.hour

            # Market session times (UTC)
            # Asian: 22:00 - 08:00 UTC (Tokyo/Sydney)
            # London: 07:00 - 16:00 UTC
            # New York: 12:00 - 21:00 UTC
            # Overlap: 12:00 - 16:00 UTC (London + NY)

            if 12 <= hour < 16:  # 12:00-16:00 UTC
                return MarketSession.OVERLAP_LONDON_NY
            elif 7 <= hour < 12:  # 07:00-12:00 UTC
                return MarketSession.LONDON
            elif 16 <= hour < 21:  # 16:00-21:00 UTC
                return MarketSession.NEW_YORK
            elif hour >= 22 or hour < 7:  # 22:00-07:00 UTC
                return MarketSession.ASIAN
            else:
                return MarketSession.QUIET

        except Exception:
            return MarketSession.QUIET

    @staticmethod
    def get_session_characteristics(session: MarketSession) -> Dict[str, any]:
        """Get trading characteristics for each market session"""
        characteristics = {
            MarketSession.ASIAN: {
                'volatility': 'low',
                'trend_strength': 'weak',
                'preferred_strategy': 'range_trading',
                'breakout_reliability': 0.3,
                'reversal_probability': 0.7,
                'confidence_multiplier': 0.8,
                'description': 'Low volatility, range-bound, mean reversion favored'
            },
            MarketSession.LONDON: {
                'volatility': 'high',
                'trend_strength': 'strong',
                'preferred_strategy': 'trend_following',
                'breakout_reliability': 0.8,
                'reversal_probability': 0.3,
                'confidence_multiplier': 1.2,
                'description': 'High volatility, strong trends, breakouts reliable'
            },
            MarketSession.NEW_YORK: {
                'volatility': 'medium_high',
                'trend_strength': 'medium',
                'preferred_strategy': 'momentum_trading',
                'breakout_reliability': 0.6,
                'reversal_probability': 0.4,
                'confidence_multiplier': 1.1,
                'description': 'Medium-high volatility, momentum-driven moves'
            },
            MarketSession.OVERLAP_LONDON_NY: {
                'volatility': 'very_high',
                'trend_strength': 'very_strong',
                'preferred_strategy': 'breakout_trading',
                'breakout_reliability': 0.9,
                'reversal_probability': 0.2,
                'confidence_multiplier': 1.3,
                'description': 'Highest volatility, strongest trends, best breakouts'
            },
            MarketSession.QUIET: {
                'volatility': 'very_low',
                'trend_strength': 'very_weak',
                'preferred_strategy': 'avoid_trading',
                'breakout_reliability': 0.2,
                'reversal_probability': 0.8,
                'confidence_multiplier': 0.6,
                'description': 'Very low activity, avoid trading'
            }
        }

        return characteristics.get(session, characteristics[MarketSession.QUIET])

    @staticmethod
    def adjust_signal_for_session(signal: Signal, confidence: float, session: MarketSession) -> Tuple[Signal, float]:
        """Adjust trading signal based on current market session"""
        characteristics = MarketSessionAnalyzer.get_session_characteristics(session)

        # Apply session-specific confidence multiplier
        adjusted_confidence = confidence * characteristics['confidence_multiplier']
        adjusted_confidence = min(1.0, max(0.0, adjusted_confidence))  # Keep within bounds

        # Session-specific signal filtering
        if session == MarketSession.QUIET:
            # During quiet periods, be conservative but not too aggressive
            if adjusted_confidence < 0.6:
                return Signal.NEUTRAL, 0.0

        elif session == MarketSession.ASIAN:
            # Asian session favors range trading and reversals
            # Reduce confidence for trend-following signals
            if signal != Signal.NEUTRAL:
                adjusted_confidence *= 0.9  # Slight reduction for directional signals

        elif session == MarketSession.OVERLAP_LONDON_NY:
            # Overlap session is best for breakouts and trends
            # Boost confidence for strong signals
            if adjusted_confidence > 0.7:
                adjusted_confidence *= 1.1

        return signal, min(1.0, adjusted_confidence)

    @staticmethod
    def get_session_optimal_expiry(session: MarketSession, base_expiry: int) -> int:
        """Get session-optimized expiry time"""
        session_multipliers = {
            MarketSession.ASIAN: 1.2,          # Slower moves, longer expiry
            MarketSession.LONDON: 0.9,         # Fast moves, shorter expiry
            MarketSession.NEW_YORK: 1.0,       # Standard timing
            MarketSession.OVERLAP_LONDON_NY: 0.8,  # Very fast moves, shortest expiry
            MarketSession.QUIET: 1.5           # Very slow moves, much longer expiry
        }

        multiplier = session_multipliers.get(session, 1.0)
        adjusted_expiry = int(base_expiry * multiplier)

        # Enhanced bounds for maximum accuracy - ONLY 60s and 90s allowed
        # 60s works better for high-momentum moves, 90s for moderate moves
        if adjusted_expiry <= 75:
            return 60  # Quick expiry for fast market moves
        else:
            return 90  # Moderate expiry for slower moves


class FalseSignalFilter:
    """🛡️ False Signal Detection & Filtering Engine"""

    def __init__(self):
        # Track patterns that commonly lead to false signals (reduced risk scores for testing)
        self.false_signal_patterns = {
            'low_volume_breakout': 0.3,      # Breakouts without volume often fail
            'news_time_volatility': 0.4,     # Signals during news are unreliable
            'weekend_gaps': 0.3,             # Weekend gap signals often reverse
            'session_transition': 0.2,       # Signals during session changes
            'conflicting_timeframes': 0.4,   # When timeframes disagree
            'weak_support_resistance': 0.3,  # Weak S/R levels often break
            'overextended_moves': 0.3,       # Signals after big moves often fail
            'low_confidence_confluence': 0.4 # Multiple weak signals together
        }

    def detect_false_signal_patterns(self, data: pd.DataFrame, signal: Signal,
                                   confidence: float, analysis_details: Dict) -> Dict[str, any]:
        """Detect patterns that commonly lead to false signals"""
        false_patterns = []
        risk_score = 0.0

        if len(data) < 10:
            return {'patterns': [], 'risk_score': 0.0, 'filtered_confidence': confidence}

        # Pattern 1: Low volume breakout (using range as volume proxy)
        recent_ranges = [(candle['high'] - candle['low']) for _, candle in data.tail(5).iterrows()]
        avg_range = sum(recent_ranges) / len(recent_ranges)
        current_range = recent_ranges[-1]

        if current_range < avg_range * 0.7:  # Low "volume" breakout
            if 'breakout' in str(analysis_details.get('breakout_analysis', {})):
                false_patterns.append('low_volume_breakout')
                risk_score += self.false_signal_patterns['low_volume_breakout']

        # Pattern 2: Overextended moves
        price_change = abs(data['close'].iloc[-1] - data['close'].iloc[-10]) / data['close'].iloc[-10]
        if price_change > 0.02:  # More than 2% move in 10 candles
            false_patterns.append('overextended_moves')
            risk_score += self.false_signal_patterns['overextended_moves']

        # Pattern 3: Weak support/resistance levels
        sr_analysis = analysis_details.get('support_resistance', {})
        if sr_analysis and 'strength' in sr_analysis:
            if sr_analysis['strength'] < 0.3:  # Weak S/R level
                false_patterns.append('weak_support_resistance')
                risk_score += self.false_signal_patterns['weak_support_resistance']

        # Pattern 4: Conflicting signals (low confluence)
        if confidence < 0.6 and signal != Signal.NEUTRAL:
            false_patterns.append('low_confidence_confluence')
            risk_score += self.false_signal_patterns['low_confidence_confluence']

        # Pattern 5: Session transition periods (first/last 30 minutes of major sessions)
        current_session = MarketSessionAnalyzer.get_current_market_session()
        utc_hour = datetime.now(timezone.utc).hour
        utc_minute = datetime.now(timezone.utc).minute

        # Check if we're in transition periods
        transition_times = [
            (6, 30, 7, 30),   # Before London open
            (11, 30, 12, 30), # London/NY transition
            (15, 30, 16, 30), # London close
            (20, 30, 21, 30), # NY close
        ]

        for start_h, start_m, end_h, end_m in transition_times:
            if (start_h < utc_hour < end_h) or \
               (utc_hour == start_h and utc_minute >= start_m) or \
               (utc_hour == end_h and utc_minute <= end_m):
                false_patterns.append('session_transition')
                risk_score += self.false_signal_patterns['session_transition']
                break

        # Calculate filtered confidence
        risk_multiplier = max(0.1, 1.0 - (risk_score / len(false_patterns)) if false_patterns else 1.0)
        filtered_confidence = confidence * risk_multiplier

        return {
            'patterns': false_patterns,
            'risk_score': risk_score,
            'filtered_confidence': filtered_confidence,
            'risk_multiplier': risk_multiplier
        }

    def should_filter_signal(self, false_signal_analysis: Dict, min_confidence: float = 0.5) -> bool:
        """Determine if signal should be filtered out due to false signal risk"""
        filtered_confidence = false_signal_analysis.get('filtered_confidence', 0.0)
        risk_score = false_signal_analysis.get('risk_score', 0.0)

        # Filter if confidence drops too low or risk is too high (less aggressive)
        return filtered_confidence < min_confidence or risk_score > 2.0


class CandlePsychologyAnalyzer:
    """🕯️ Advanced Candle Psychology & Pattern Analysis Engine"""

    @staticmethod
    def detect_advanced_reversal_patterns(data: pd.DataFrame) -> Dict[str, any]:
        """🔄 Detect advanced reversal patterns (Hammer, Shooting Star, Spinning Top, etc.)"""
        if len(data) < 3:
            return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

        current = data.iloc[-1]
        previous = data.iloc[-2]

        open_price = current['open']
        high_price = current['high']
        low_price = current['low']
        close_price = current['close']

        body = abs(close_price - open_price)
        total_range = high_price - low_price
        upper_wick = high_price - max(open_price, close_price)
        lower_wick = min(open_price, close_price) - low_price

        if total_range == 0:
            return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

        body_ratio = body / total_range
        upper_wick_ratio = upper_wick / total_range
        lower_wick_ratio = lower_wick / total_range

        # Hammer Pattern (bullish reversal)
        if (lower_wick_ratio > 0.6 and body_ratio < 0.3 and upper_wick_ratio < 0.1 and
            previous['close'] < previous['open']):  # Previous candle bearish
            return {
                'pattern': 'hammer',
                'strength': min(0.9, lower_wick_ratio + (1 - body_ratio)),
                'signal': 'up',
                'description': 'Hammer - Strong bullish reversal signal'
            }

        # Shooting Star Pattern (bearish reversal)
        if (upper_wick_ratio > 0.6 and body_ratio < 0.3 and lower_wick_ratio < 0.1 and
            previous['close'] > previous['open']):  # Previous candle bullish
            return {
                'pattern': 'shooting_star',
                'strength': min(0.9, upper_wick_ratio + (1 - body_ratio)),
                'signal': 'down',
                'description': 'Shooting Star - Strong bearish reversal signal'
            }

        # Inverted Hammer (bullish reversal in downtrend)
        if (upper_wick_ratio > 0.6 and body_ratio < 0.3 and lower_wick_ratio < 0.1):
            # Check if we're in a downtrend
            recent_trend = data.tail(5)['close'].diff().mean()
            if recent_trend < 0:
                return {
                    'pattern': 'inverted_hammer',
                    'strength': min(0.85, upper_wick_ratio + (1 - body_ratio)),
                    'signal': 'up',
                    'description': 'Inverted Hammer - Bullish reversal in downtrend'
                }

        # Hanging Man (bearish reversal in uptrend)
        if (lower_wick_ratio > 0.6 and body_ratio < 0.3 and upper_wick_ratio < 0.1):
            # Check if we're in an uptrend
            recent_trend = data.tail(5)['close'].diff().mean()
            if recent_trend > 0:
                return {
                    'pattern': 'hanging_man',
                    'strength': min(0.85, lower_wick_ratio + (1 - body_ratio)),
                    'signal': 'down',
                    'description': 'Hanging Man - Bearish reversal in uptrend'
                }

        # Spinning Top (indecision)
        if body_ratio < 0.2 and upper_wick_ratio > 0.3 and lower_wick_ratio > 0.3:
            return {
                'pattern': 'spinning_top',
                'strength': min(0.7, (upper_wick_ratio + lower_wick_ratio) / 2),
                'signal': 'neutral',
                'description': 'Spinning Top - Market indecision'
            }

        return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

    @staticmethod
    def detect_advanced_continuation_patterns(data: pd.DataFrame) -> Dict[str, any]:
        """📈 Detect advanced continuation patterns (Marubozu, Belt Hold, etc.)"""
        if len(data) < 2:
            return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

        current = data.iloc[-1]

        open_price = current['open']
        high_price = current['high']
        low_price = current['low']
        close_price = current['close']

        body = abs(close_price - open_price)
        total_range = high_price - low_price
        upper_wick = high_price - max(open_price, close_price)
        lower_wick = min(open_price, close_price) - low_price

        if total_range == 0:
            return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

        body_ratio = body / total_range
        upper_wick_ratio = upper_wick / total_range
        lower_wick_ratio = lower_wick / total_range

        # White/Green Marubozu (strong bullish continuation)
        if (close_price > open_price and body_ratio > 0.9 and
            upper_wick_ratio < 0.05 and lower_wick_ratio < 0.05):
            return {
                'pattern': 'white_marubozu',
                'strength': min(0.95, body_ratio),
                'signal': 'up',
                'description': 'White Marubozu - Strong bullish continuation'
            }

        # Black/Red Marubozu (strong bearish continuation)
        if (close_price < open_price and body_ratio > 0.9 and
            upper_wick_ratio < 0.05 and lower_wick_ratio < 0.05):
            return {
                'pattern': 'black_marubozu',
                'strength': min(0.95, body_ratio),
                'signal': 'down',
                'description': 'Black Marubozu - Strong bearish continuation'
            }

        # White Belt Hold (bullish continuation)
        if (close_price > open_price and body_ratio > 0.7 and
            lower_wick_ratio < 0.05 and open_price == low_price):
            return {
                'pattern': 'white_belt_hold',
                'strength': min(0.85, body_ratio),
                'signal': 'up',
                'description': 'White Belt Hold - Bullish continuation'
            }

        # Black Belt Hold (bearish continuation)
        if (close_price < open_price and body_ratio > 0.7 and
            upper_wick_ratio < 0.05 and open_price == high_price):
            return {
                'pattern': 'black_belt_hold',
                'strength': min(0.85, body_ratio),
                'signal': 'down',
                'description': 'Black Belt Hold - Bearish continuation'
            }

        return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

    @staticmethod
    def detect_multi_candle_patterns(data: pd.DataFrame) -> Dict[str, any]:
        """🕯️🕯️🕯️ Detect advanced multi-candle patterns"""
        if len(data) < 3:
            return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

        # Get last 3 candles
        c1, c2, c3 = data.iloc[-3], data.iloc[-2], data.iloc[-1]

        # Morning Star Pattern (bullish reversal)
        if (c1['close'] < c1['open'] and  # First candle bearish
            abs(c2['close'] - c2['open']) < (c1['high'] - c1['low']) * 0.3 and  # Second candle small body
            c3['close'] > c3['open'] and  # Third candle bullish
            c3['close'] > (c1['open'] + c1['close']) / 2):  # Third closes above midpoint of first

            strength = min(0.9, (c3['close'] - c3['open']) / (c3['high'] - c3['low']))
            return {
                'pattern': 'morning_star',
                'strength': strength,
                'signal': 'up',
                'description': 'Morning Star - Strong bullish reversal'
            }

        # Evening Star Pattern (bearish reversal)
        if (c1['close'] > c1['open'] and  # First candle bullish
            abs(c2['close'] - c2['open']) < (c1['high'] - c1['low']) * 0.3 and  # Second candle small body
            c3['close'] < c3['open'] and  # Third candle bearish
            c3['close'] < (c1['open'] + c1['close']) / 2):  # Third closes below midpoint of first

            strength = min(0.9, (c3['open'] - c3['close']) / (c3['high'] - c3['low']))
            return {
                'pattern': 'evening_star',
                'strength': strength,
                'signal': 'down',
                'description': 'Evening Star - Strong bearish reversal'
            }

        # Three Inside Up (bullish reversal)
        if (c1['close'] < c1['open'] and  # First candle bearish
            c2['close'] > c2['open'] and  # Second candle bullish
            c2['open'] > c1['close'] and c2['close'] < c1['open'] and  # Second inside first
            c3['close'] > c3['open'] and c3['close'] > c1['open']):  # Third confirms

            return {
                'pattern': 'three_inside_up',
                'strength': 0.8,
                'signal': 'up',
                'description': 'Three Inside Up - Bullish reversal confirmation'
            }

        # Three Inside Down (bearish reversal)
        if (c1['close'] > c1['open'] and  # First candle bullish
            c2['close'] < c2['open'] and  # Second candle bearish
            c2['open'] < c1['close'] and c2['close'] > c1['open'] and  # Second inside first
            c3['close'] < c3['open'] and c3['close'] < c1['open']):  # Third confirms

            return {
                'pattern': 'three_inside_down',
                'strength': 0.8,
                'signal': 'down',
                'description': 'Three Inside Down - Bearish reversal confirmation'
            }

        return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

    @staticmethod
    def analyze_candle_type(candle: pd.Series) -> CandleType:
        """Identify the type of individual candle"""
        open_price = candle['open']
        high_price = candle['high']
        low_price = candle['low']
        close_price = candle['close']

        body = abs(close_price - open_price)
        total_range = high_price - low_price
        upper_wick = high_price - max(open_price, close_price)
        lower_wick = min(open_price, close_price) - low_price

        # Avoid division by zero
        if total_range == 0:
            return CandleType.DOJI

        body_ratio = body / total_range
        upper_wick_ratio = upper_wick / total_range
        lower_wick_ratio = lower_wick / total_range

        # Doji (small body)
        if body_ratio < 0.1:
            return CandleType.DOJI

        # Marubozu (no wicks)
        if upper_wick_ratio < 0.05 and lower_wick_ratio < 0.05:
            return CandleType.MARUBOZU_BULL if close_price > open_price else CandleType.MARUBOZU_BEAR

        # Pin Bar (long wick, small body)
        if lower_wick_ratio > 0.6 and body_ratio < 0.3:
            return CandleType.PIN_BAR_BULL
        elif upper_wick_ratio > 0.6 and body_ratio < 0.3:
            return CandleType.PIN_BAR_BEAR

        # Hammer/Hanging Man
        if lower_wick_ratio > 0.5 and upper_wick_ratio < 0.2 and body_ratio < 0.4:
            return CandleType.HAMMER

        # Shooting Star/Inverted Hammer
        if upper_wick_ratio > 0.5 and lower_wick_ratio < 0.2 and body_ratio < 0.4:
            return CandleType.SHOOTING_STAR

        # Regular bullish/bearish
        return CandleType.BULLISH if close_price > open_price else CandleType.BEARISH

    @staticmethod
    def analyze_wick_psychology(data: pd.DataFrame) -> Dict[str, float]:
        """Analyze wick patterns for buyer/seller strength"""
        if len(data) < 3:
            return {'buyer_strength': 0.5, 'seller_strength': 0.5, 'indecision': 0.0}

        recent_candles = data.tail(3)
        total_buyer_strength = 0
        total_seller_strength = 0
        total_indecision = 0

        for _, candle in recent_candles.iterrows():
            body = abs(candle['close'] - candle['open'])
            total_range = candle['high'] - candle['low']
            upper_wick = candle['high'] - max(candle['open'], candle['close'])
            lower_wick = min(candle['open'], candle['close']) - candle['low']

            if total_range > 0:
                # Lower wick = buyer strength (buyers stepped in)
                buyer_strength = lower_wick / total_range
                # Upper wick = seller strength (sellers stepped in)
                seller_strength = upper_wick / total_range
                # Small body = indecision
                indecision = 1 - (body / total_range)

                total_buyer_strength += buyer_strength
                total_seller_strength += seller_strength
                total_indecision += indecision

        count = len(recent_candles)
        return {
            'buyer_strength': total_buyer_strength / count,
            'seller_strength': total_seller_strength / count,
            'indecision': total_indecision / count
        }

    @staticmethod
    def detect_doji_variations(data: pd.DataFrame) -> Dict[str, any]:
        """Detect Doji variations (Gravestone, Dragonfly, Long-legged)"""
        if len(data) < 1:
            return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

        current = data.iloc[-1]
        open_price = current['open']
        high_price = current['high']
        low_price = current['low']
        close_price = current['close']

        body = abs(close_price - open_price)
        total_range = high_price - low_price

        if total_range == 0:
            return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

        body_ratio = body / total_range
        upper_wick = high_price - max(open_price, close_price)
        lower_wick = min(open_price, close_price) - low_price
        upper_wick_ratio = upper_wick / total_range
        lower_wick_ratio = lower_wick / total_range

        # Doji (very small body, wicks on both sides)
        if body_ratio < 0.1 and upper_wick_ratio > 0.1 and lower_wick_ratio > 0.1:
            return {
                'pattern': 'doji',
                'strength': 0.5,
                'signal': 'neutral',
                'description': 'Doji - Indecision'
            }

        # Gravestone Doji (small body at low, long upper wick, no/small lower wick)
        # Indicates bearish reversal
        if body_ratio < 0.1 and upper_wick_ratio > 0.6 and lower_wick_ratio < 0.1:
            return {
                'pattern': 'gravestone_doji',
                'strength': 0.8,
                'signal': 'down',
                'description': 'Gravestone Doji - Bearish reversal'
            }

        # Dragonfly Doji (small body at high, long lower wick, no/small upper wick)
        # Indicates bullish reversal
        if body_ratio < 0.1 and lower_wick_ratio > 0.6 and upper_wick_ratio < 0.1:
            return {
                'pattern': 'dragonfly_doji',
                'strength': 0.8,
                'signal': 'up',
                'description': 'Dragonfly Doji - Bullish reversal'
            }

        # Long-legged Doji (small body, very long upper and lower wicks)
        # Indicates strong indecision and potential reversal
        if body_ratio < 0.1 and upper_wick_ratio > 0.4 and lower_wick_ratio > 0.4:
            return {
                'pattern': 'long_legged_doji',
                'strength': 0.6,
                'signal': 'neutral',
                'description': 'Long-legged Doji - Strong indecision'
            }

        return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

    @staticmethod
    def detect_tweezer_patterns(data: pd.DataFrame) -> Dict[str, any]:
        """Detect Tweezer Tops and Tweezer Bottoms patterns"""
        if len(data) < 2:
            return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

        c1, c2 = data.iloc[-2], data.iloc[-1]

        # Tweezer Top (bearish reversal)
        # Two consecutive candles with almost identical highs
        if (abs(c1['high'] - c2['high']) / c1['high'] < 0.0005 and  # Highs are very close
            c1['close'] > c1['open'] and  # First candle bullish
            c2['close'] < c2['open'] and  # Second candle bearish
            c1['high'] == c2['high']): # Exact same high for stronger signal
            return {
                'pattern': 'tweezer_top',
                'strength': 0.85,
                'signal': 'down',
                'description': 'Tweezer Top - Bearish reversal'
            }

        # Tweezer Bottom (bullish reversal)
        # Two consecutive candles with almost identical lows
        if (abs(c1['low'] - c2['low']) / c1['low'] < 0.0005 and  # Lows are very close
            c1['close'] < c1['open'] and  # First candle bearish
            c2['close'] > c2['open'] and  # Second candle bullish
            c1['low'] == c2['low']): # Exact same low for stronger signal
            return {
                'pattern': 'tweezer_bottom',
                'strength': 0.85,
                'signal': 'up',
                'description': 'Tweezer Bottom - Bullish reversal'
            }

        return {'pattern': 'none', 'strength': 0.0, 'signal': 'neutral'}

    @staticmethod
    def detect_engulfing_patterns(data: pd.DataFrame) -> Tuple[bool, str, float]:
        """Detect bullish/bearish engulfing patterns"""
        if len(data) < 2:
            return False, "none", 0.0

        current = data.iloc[-1]
        previous = data.iloc[-2]

        current_body = abs(current['close'] - current['open'])
        previous_body = abs(previous['close'] - previous['open'])

        # Bullish engulfing
        if (previous['close'] < previous['open'] and  # Previous bearish
            current['close'] > current['open'] and   # Current bullish
            current['open'] < previous['close'] and  # Opens below previous close
            current['close'] > previous['open']):    # Closes above previous open

            strength = min(0.9, current_body / previous_body)
            return True, "bullish_engulfing", strength

        # Bearish engulfing
        elif (previous['close'] > previous['open'] and  # Previous bullish
              current['close'] < current['open'] and   # Current bearish
              current['open'] > previous['close'] and  # Opens above previous close
              current['close'] < previous['open']):    # Closes below previous open

            strength = min(0.9, current_body / previous_body)
            return True, "bearish_engulfing", strength

        return False, "none", 0.0

    @staticmethod
    def detect_three_soldiers_crows(data: pd.DataFrame) -> Tuple[bool, str, float]:
        """Detect Three White Soldiers / Three Black Crows patterns"""
        if len(data) < 3:
            return False, "none", 0.0

        last_three = data.tail(3)
        candles = []

        for _, candle in last_three.iterrows():
            candles.append({
                'bullish': candle['close'] > candle['open'],
                'body': abs(candle['close'] - candle['open']),
                'close': candle['close']
            })

        # Three White Soldiers
        if all(c['bullish'] for c in candles):
            # Each candle should close higher than previous
            ascending = all(candles[i]['close'] > candles[i-1]['close'] for i in range(1, 3))
            # Bodies should be substantial
            avg_body = sum(c['body'] for c in candles) / 3
            min_body = min(c['body'] for c in candles)

            if ascending and min_body > avg_body * 0.7:
                strength = min(0.9, avg_body / data['close'].std())
                return True, "three_white_soldiers", strength

        # Three Black Crows
        elif not any(c['bullish'] for c in candles):
            # Each candle should close lower than previous
            descending = all(candles[i]['close'] < candles[i-1]['close'] for i in range(1, 3))
            # Bodies should be substantial
            avg_body = sum(c['body'] for c in candles) / 3
            min_body = min(c['body'] for c in candles)

            if descending and min_body > avg_body * 0.7:
                strength = min(0.9, avg_body / data['close'].std())
                return True, "three_black_crows", strength

        return False, "none", 0.0

    @staticmethod
    def analyze_candle_sequence(data: pd.DataFrame) -> Dict[str, any]:
        """Analyze sequence patterns in recent candles"""
        if len(data) < 5:
            return {'pattern': 'insufficient_data', 'strength': 0.0, 'signal': 'neutral'}

        recent = data.tail(5)
        bullish_count = sum(1 for _, candle in recent.iterrows() if candle['close'] > candle['open'])
        bearish_count = 5 - bullish_count

        # Strong bullish sequence
        if bullish_count >= 4:
            return {'pattern': 'strong_bullish_sequence', 'strength': 0.8, 'signal': 'up'}

        # Strong bearish sequence
        elif bearish_count >= 4:
            return {'pattern': 'strong_bearish_sequence', 'strength': 0.8, 'signal': 'down'}

        # Alternating pattern (indecision)
        elif abs(bullish_count - bearish_count) <= 1:
            return {'pattern': 'alternating_indecision', 'strength': 0.3, 'signal': 'neutral'}

        # Reversal pattern (2-3 same direction, then opposite)
        last_two = data.tail(2)
        if len(last_two) == 2:
            last_bullish = last_two.iloc[-1]['close'] > last_two.iloc[-1]['open']
            prev_bullish = last_two.iloc[-2]['close'] > last_two.iloc[-2]['open']

            if last_bullish != prev_bullish:
                return {'pattern': 'potential_reversal', 'strength': 0.6, 'signal': 'up' if last_bullish else 'down'}

        return {'pattern': 'mixed', 'strength': 0.4, 'signal': 'neutral'}

    @staticmethod
    def calculate_momentum_from_candles(data: pd.DataFrame) -> Dict[str, float]:
        """Calculate momentum indicators from candle analysis"""
        if len(data) < 10:
            return {'range_expansion': 0.0, 'body_momentum': 0.0, 'wick_momentum': 0.0}

        recent = data.tail(10)

        # Range expansion/contraction
        ranges = [(candle['high'] - candle['low']) for _, candle in recent.iterrows()]
        avg_range = sum(ranges) / len(ranges)
        current_range = ranges[-1]
        range_expansion = (current_range - avg_range) / avg_range if avg_range > 0 else 0

        # Body momentum (increasing/decreasing body sizes)
        bodies = [abs(candle['close'] - candle['open']) for _, candle in recent.iterrows()]
        body_momentum = 0
        for i in range(1, len(bodies)):
            if bodies[i] > bodies[i-1]:
                body_momentum += 0.1
            else:
                body_momentum -= 0.1

        # Wick momentum (wick patterns)
        wick_momentum = 0
        for _, candle in recent.iterrows():
            upper_wick = candle['high'] - max(candle['open'], candle['close'])
            lower_wick = min(candle['open'], candle['close']) - candle['low']
            total_range = candle['high'] - candle['low']

            if total_range > 0:
                if lower_wick > upper_wick:
                    wick_momentum += 0.1  # Bullish (buyers stepping in)
                else:
                    wick_momentum -= 0.1  # Bearish (sellers stepping in)

        return {
            'range_expansion': max(-1, min(1, range_expansion)),
            'body_momentum': max(-1, min(1, body_momentum)),
            'wick_momentum': max(-1, min(1, wick_momentum))
        }

    @staticmethod
    def analyze_market_psychology_from_candles(data: pd.DataFrame) -> Dict[str, any]:
        """🧠 Analyze market psychology from candlestick patterns"""
        if len(data) < 10:
            return {'psychology': 'neutral', 'strength': 0.0, 'emotion': 'none'}

        recent_candles = data.tail(10)

        # Analyze fear patterns
        fear_indicators = 0
        greed_indicators = 0
        indecision_indicators = 0

        for _, candle in recent_candles.iterrows():
            body = abs(candle['close'] - candle['open'])
            total_range = candle['high'] - candle['low']

            if total_range > 0:
                body_ratio = body / total_range
                upper_wick = candle['high'] - max(candle['open'], candle['close'])
                lower_wick = min(candle['open'], candle['close']) - candle['low']
                upper_wick_ratio = upper_wick / total_range
                lower_wick_ratio = lower_wick / total_range

                # Fear indicators (long lower wicks, small bodies)
                if lower_wick_ratio > 0.5 and body_ratio < 0.3:
                    fear_indicators += 1

                # Greed indicators (strong bodies, minimal wicks)
                if body_ratio > 0.8 and upper_wick_ratio < 0.1 and lower_wick_ratio < 0.1:
                    greed_indicators += 1

                # Indecision indicators (small bodies, long wicks)
                if body_ratio < 0.2 and (upper_wick_ratio > 0.3 or lower_wick_ratio > 0.3):
                    indecision_indicators += 1

        # Determine dominant psychology
        total_candles = len(recent_candles)
        fear_ratio = fear_indicators / total_candles
        greed_ratio = greed_indicators / total_candles
        indecision_ratio = indecision_indicators / total_candles

        if fear_ratio >= 0.4:
            return {
                'psychology': 'fear',
                'strength': min(0.9, fear_ratio),
                'emotion': 'fearful',
                'signal': 'up',  # Fear often leads to bullish reversal
                'description': f'Market showing fear: {fear_indicators}/{total_candles} fear patterns'
            }
        elif greed_ratio >= 0.4:
            return {
                'psychology': 'greed',
                'strength': min(0.9, greed_ratio),
                'emotion': 'greedy',
                'signal': 'down',  # Greed often leads to bearish reversal
                'description': f'Market showing greed: {greed_indicators}/{total_candles} greed patterns'
            }
        elif indecision_ratio >= 0.5:
            return {
                'psychology': 'indecision',
                'strength': min(0.8, indecision_ratio),
                'emotion': 'uncertain',
                'signal': 'neutral',
                'description': f'Market indecision: {indecision_indicators}/{total_candles} indecision patterns'
            }

        return {'psychology': 'neutral', 'strength': 0.0, 'emotion': 'none'}

    @staticmethod
    def detect_emotional_exhaustion_patterns(data: pd.DataFrame) -> Dict[str, any]:
        """😴 Detect emotional exhaustion in candlestick patterns"""
        if len(data) < 5:
            return {'exhaustion': False, 'type': 'none', 'strength': 0.0}

        recent = data.tail(5)

        # Look for climax patterns (large candles followed by reversal)
        exhaustion_patterns = []

        for i in range(len(recent) - 1):
            current = recent.iloc[i]
            next_candle = recent.iloc[i + 1]

            current_body = abs(current['close'] - current['open'])
            current_range = current['high'] - current['low']

            if current_range > 0:
                body_ratio = current_body / current_range

                # Large candle (potential climax)
                if body_ratio > 0.8:
                    # Check for reversal in next candle
                    current_bullish = current['close'] > current['open']
                    next_bullish = next_candle['close'] > next_candle['open']

                    if current_bullish != next_bullish:
                        exhaustion_type = 'bullish_exhaustion' if current_bullish else 'bearish_exhaustion'
                        exhaustion_patterns.append({
                            'type': exhaustion_type,
                            'strength': body_ratio,
                            'signal': 'down' if current_bullish else 'up'
                        })

        if exhaustion_patterns:
            strongest = max(exhaustion_patterns, key=lambda x: x['strength'])
            return {
                'exhaustion': True,
                'type': strongest['type'],
                'strength': strongest['strength'],
                'signal': strongest['signal'],
                'description': f'Emotional exhaustion detected: {strongest["type"]}'
            }

        return {'exhaustion': False, 'type': 'none', 'strength': 0.0}

    @staticmethod
    def analyze_commitment_vs_indecision(data: pd.DataFrame) -> Dict[str, any]:
        """⚖️ Analyze market commitment vs indecision from candle bodies and wicks"""
        if len(data) < 8:
            return {'analysis': 'insufficient_data', 'commitment_score': 0.0, 'indecision_score': 0.0}

        recent = data.tail(8)
        commitment_score = 0
        indecision_score = 0

        for _, candle in recent.iterrows():
            body = abs(candle['close'] - candle['open'])
            total_range = candle['high'] - candle['low']

            if total_range > 0:
                body_ratio = body / total_range
                upper_wick = candle['high'] - max(candle['open'], candle['close'])
                lower_wick = min(candle['open'], candle['close']) - candle['low']
                wick_ratio = (upper_wick + lower_wick) / total_range

                # Strong commitment (large body, small wicks)
                if body_ratio > 0.7:
                    commitment_score += body_ratio

                # Indecision (small body, large wicks)
                if body_ratio < 0.3 and wick_ratio > 0.5:
                    indecision_score += wick_ratio

        # Normalize scores
        total_candles = len(recent)
        commitment_score /= total_candles
        indecision_score /= total_candles

        if commitment_score > indecision_score * 1.5:
            return {
                'analysis': 'strong_commitment',
                'commitment_score': commitment_score,
                'indecision_score': indecision_score,
                'signal': 'continuation',
                'description': f'Strong market commitment detected (score: {commitment_score:.2f})'
            }
        elif indecision_score > commitment_score * 1.5:
            return {
                'analysis': 'high_indecision',
                'commitment_score': commitment_score,
                'indecision_score': indecision_score,
                'signal': 'reversal_likely',
                'description': f'High market indecision detected (score: {indecision_score:.2f})'
            }
        else:
            return {
                'analysis': 'balanced',
                'commitment_score': commitment_score,
                'indecision_score': indecision_score,
                'signal': 'neutral',
                'description': 'Balanced commitment and indecision'
            }


class EnhancedMultiTimeframeAnalyzer:
    """📊 ENHANCED MULTI-TIMEFRAME CONFLUENCE ANALYSIS ENGINE 📊"""

    @staticmethod
    def calculate_timeframe_synchronization(tf1_data: pd.DataFrame, tf5_data: pd.DataFrame, tf15_data: pd.DataFrame) -> Dict[str, float]:
        """Calculate synchronization scores between timeframes"""
        try:
            sync_scores = {}

            # Trend synchronization using KAMA
            math_indicators = AdvancedMathematicalIndicators()

            # Calculate KAMA for each timeframe
            kama_1m = math_indicators.calculate_kama(tf1_data, period=10)
            kama_5m = math_indicators.calculate_kama(tf5_data, period=10)
            kama_15m = math_indicators.calculate_kama(tf15_data, period=10)

            # Calculate trend directions
            trend_1m = 1 if kama_1m.iloc[-1] > kama_1m.iloc[-2] else -1
            trend_5m = 1 if kama_5m.iloc[-1] > kama_5m.iloc[-2] else -1
            trend_15m = 1 if kama_15m.iloc[-1] > kama_15m.iloc[-2] else -1

            # Synchronization score (0-1)
            trend_alignment = (trend_1m == trend_5m) + (trend_5m == trend_15m) + (trend_1m == trend_15m)
            sync_scores['trend_sync'] = trend_alignment / 3.0

            # Momentum synchronization using ZLEMA
            zlema_1m = math_indicators.calculate_zlema(tf1_data, period=14)
            zlema_5m = math_indicators.calculate_zlema(tf5_data, period=14)
            zlema_15m = math_indicators.calculate_zlema(tf15_data, period=14)

            # Calculate momentum directions
            momentum_1m = 1 if zlema_1m.iloc[-1] > tf1_data['close'].iloc[-1] else -1
            momentum_5m = 1 if zlema_5m.iloc[-1] > tf5_data['close'].iloc[-1] else -1
            momentum_15m = 1 if zlema_15m.iloc[-1] > tf15_data['close'].iloc[-1] else -1

            momentum_alignment = (momentum_1m == momentum_5m) + (momentum_5m == momentum_15m) + (momentum_1m == momentum_15m)
            sync_scores['momentum_sync'] = momentum_alignment / 3.0

            # Overall synchronization
            sync_scores['overall_sync'] = (sync_scores['trend_sync'] + sync_scores['momentum_sync']) / 2.0

            return sync_scores

        except:
            return {'trend_sync': 0.5, 'momentum_sync': 0.5, 'overall_sync': 0.5}

    @staticmethod
    def analyze_fractal_alignment(tf1_data: pd.DataFrame, tf5_data: pd.DataFrame, tf15_data: pd.DataFrame) -> Dict[str, any]:
        """Analyze fractal patterns across timeframes"""
        try:
            math_indicators = AdvancedMathematicalIndicators()

            # Calculate FRAMA for each timeframe
            frama_1m = math_indicators.calculate_frama(tf1_data, period=14)
            frama_5m = math_indicators.calculate_frama(tf5_data, period=14)
            frama_15m = math_indicators.calculate_frama(tf15_data, period=14)

            # Analyze fractal convergence
            current_price_1m = tf1_data['close'].iloc[-1]
            current_price_5m = tf5_data['close'].iloc[-1]
            current_price_15m = tf15_data['close'].iloc[-1]

            # Distance from FRAMA (normalized)
            frama_distance_1m = abs(current_price_1m - frama_1m.iloc[-1]) / current_price_1m
            frama_distance_5m = abs(current_price_5m - frama_5m.iloc[-1]) / current_price_5m
            frama_distance_15m = abs(current_price_15m - frama_15m.iloc[-1]) / current_price_15m

            # Fractal alignment score (closer to FRAMA = higher alignment)
            alignment_score = 1.0 - np.mean([frama_distance_1m, frama_distance_5m, frama_distance_15m])
            alignment_score = max(0.0, min(1.0, alignment_score))

            return {
                'fractal_alignment': alignment_score,
                'frama_distances': {
                    '1m': frama_distance_1m,
                    '5m': frama_distance_5m,
                    '15m': frama_distance_15m
                },
                'convergence_strength': 1.0 - np.std([frama_distance_1m, frama_distance_5m, frama_distance_15m])
            }

        except:
            return {'fractal_alignment': 0.5, 'frama_distances': {}, 'convergence_strength': 0.5}


class EnhancedPatternRecognition:
    """🎯 ENHANCED PATTERN RECOGNITION ENGINE 🎯"""

    @staticmethod
    def detect_harmonic_patterns(data: pd.DataFrame) -> Dict[str, any]:
        """Detect harmonic patterns (Gartley, Butterfly, Bat, Crab)"""
        try:
            if len(data) < 20:
                return {'pattern': None, 'confidence': 0.0, 'type': 'none'}

            # Find swing points
            highs = []
            lows = []

            for i in range(5, len(data) - 5):
                # Check for swing high
                if all(data['high'].iloc[i] >= data['high'].iloc[j] for j in range(i-5, i+6) if j != i):
                    highs.append((i, data['high'].iloc[i]))

                # Check for swing low
                if all(data['low'].iloc[i] <= data['low'].iloc[j] for j in range(i-5, i+6) if j != i):
                    lows.append((i, data['low'].iloc[i]))

            # Need at least 5 points for harmonic patterns
            if len(highs) + len(lows) < 5:
                return {'pattern': None, 'confidence': 0.0, 'type': 'none'}

            # Combine and sort swing points
            swing_points = sorted(highs + lows, key=lambda x: x[0])

            if len(swing_points) < 5:
                return {'pattern': None, 'confidence': 0.0, 'type': 'none'}

            # Check for ABCD pattern (simplified)
            recent_points = swing_points[-4:]  # Last 4 swing points

            if len(recent_points) == 4:
                A, B, C, D = recent_points

                # Calculate ratios
                AB = abs(B[1] - A[1])
                BC = abs(C[1] - B[1])
                CD = abs(D[1] - C[1])

                if AB > 0 and BC > 0:
                    bc_ab_ratio = BC / AB
                    cd_bc_ratio = CD / BC if BC > 0 else 0

                    # Check for Gartley pattern (0.618 and 0.786 ratios)
                    if 0.55 <= bc_ab_ratio <= 0.68 and 0.70 <= cd_bc_ratio <= 0.85:
                        return {
                            'pattern': 'Gartley',
                            'confidence': 0.75,
                            'type': 'bullish' if D[1] < A[1] else 'bearish',
                            'ratios': {'BC/AB': bc_ab_ratio, 'CD/BC': cd_bc_ratio}
                        }

                    # Check for Butterfly pattern (0.786 and 1.27 ratios)
                    elif 0.70 <= bc_ab_ratio <= 0.85 and 1.15 <= cd_bc_ratio <= 1.40:
                        return {
                            'pattern': 'Butterfly',
                            'confidence': 0.70,
                            'type': 'bullish' if D[1] < A[1] else 'bearish',
                            'ratios': {'BC/AB': bc_ab_ratio, 'CD/BC': cd_bc_ratio}
                        }

            return {'pattern': None, 'confidence': 0.0, 'type': 'none'}

        except:
            return {'pattern': None, 'confidence': 0.0, 'type': 'none'}

    @staticmethod
    def detect_advanced_candlestick_combinations(data: pd.DataFrame) -> Dict[str, any]:
        """Detect advanced 3-5 candle combinations"""
        try:
            if len(data) < 5:
                return {'pattern': None, 'confidence': 0.0, 'signal': 'NEUTRAL'}

            recent = data.tail(5)
            patterns = []

            # Three White Soldiers / Three Black Crows (enhanced)
            if len(recent) >= 3:
                last_3 = recent.tail(3)

                # Check for three consecutive bullish candles
                bullish_candles = all(row['close'] > row['open'] for _, row in last_3.iterrows())
                bearish_candles = all(row['close'] < row['open'] for _, row in last_3.iterrows())

                if bullish_candles:
                    # Check for increasing highs and lows
                    increasing_highs = all(last_3['high'].iloc[i] > last_3['high'].iloc[i-1] for i in range(1, 3))
                    increasing_lows = all(last_3['low'].iloc[i] > last_3['low'].iloc[i-1] for i in range(1, 3))

                    if increasing_highs and increasing_lows:
                        patterns.append({
                            'pattern': 'Three White Soldiers',
                            'confidence': 0.80,
                            'signal': 'UP',
                            'strength': 'strong'
                        })

                elif bearish_candles:
                    # Check for decreasing highs and lows
                    decreasing_highs = all(last_3['high'].iloc[i] < last_3['high'].iloc[i-1] for i in range(1, 3))
                    decreasing_lows = all(last_3['low'].iloc[i] < last_3['low'].iloc[i-1] for i in range(1, 3))

                    if decreasing_highs and decreasing_lows:
                        patterns.append({
                            'pattern': 'Three Black Crows',
                            'confidence': 0.80,
                            'signal': 'DOWN',
                            'strength': 'strong'
                        })

            # Morning Star / Evening Star (enhanced)
            if len(recent) >= 3:
                first, second, third = recent.iloc[-3], recent.iloc[-2], recent.iloc[-1]

                # Morning Star
                if (first['close'] < first['open'] and  # First candle bearish
                    abs(second['close'] - second['open']) < (first['high'] - first['low']) * 0.3 and  # Second candle small
                    third['close'] > third['open'] and  # Third candle bullish
                    third['close'] > (first['open'] + first['close']) / 2):  # Third closes above first midpoint

                    patterns.append({
                        'pattern': 'Morning Star',
                        'confidence': 0.75,
                        'signal': 'UP',
                        'strength': 'strong'
                    })

                # Evening Star
                elif (first['close'] > first['open'] and  # First candle bullish
                      abs(second['close'] - second['open']) < (first['high'] - first['low']) * 0.3 and  # Second candle small
                      third['close'] < third['open'] and  # Third candle bearish
                      third['close'] < (first['open'] + first['close']) / 2):  # Third closes below first midpoint

                    patterns.append({
                        'pattern': 'Evening Star',
                        'confidence': 0.75,
                        'signal': 'DOWN',
                        'strength': 'strong'
                    })

            # Return strongest pattern
            if patterns:
                best_pattern = max(patterns, key=lambda x: x['confidence'])
                return best_pattern

            return {'pattern': None, 'confidence': 0.0, 'signal': 'NEUTRAL'}

        except:
            return {'pattern': None, 'confidence': 0.0, 'signal': 'NEUTRAL'}


class AdvancedVolatilityAnalysis:
    """📊 ADVANCED VOLATILITY ANALYSIS ENGINE 📊"""

    @staticmethod
    def calculate_smoothed_atr(data: pd.DataFrame, period: int = 14, smoothing: int = 3) -> pd.Series:
        """Calculate smoothed Average True Range for better volatility measurement"""
        try:
            # Calculate True Range
            high_low = data['high'] - data['low']
            high_close_prev = abs(data['high'] - data['close'].shift(1))
            low_close_prev = abs(data['low'] - data['close'].shift(1))

            true_range = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)

            # Calculate ATR
            atr = true_range.rolling(window=period).mean()

            # Apply additional smoothing
            smoothed_atr = atr.rolling(window=smoothing).mean()

            return smoothed_atr
        except:
            return pd.Series(index=data.index, dtype=float)

    @staticmethod
    def detect_volatility_breakouts(data: pd.DataFrame, period: int = 20, threshold: float = 1.5) -> Dict[str, any]:
        """Detect volatility breakouts and contractions"""
        try:
            if len(data) < period + 5:
                return {'breakout': False, 'contraction': False, 'volatility_state': 'normal'}

            # Calculate ATR
            atr = AdvancedVolatilityAnalysis.calculate_smoothed_atr(data, period)

            # Calculate volatility percentiles
            atr_mean = atr.rolling(window=period).mean()
            atr_std = atr.rolling(window=period).std()

            current_atr = atr.iloc[-1]
            mean_atr = atr_mean.iloc[-1]
            std_atr = atr_std.iloc[-1]

            # Z-score for current volatility
            if std_atr > 0:
                volatility_zscore = (current_atr - mean_atr) / std_atr
            else:
                volatility_zscore = 0

            # Detect breakouts and contractions
            breakout = volatility_zscore > threshold
            contraction = volatility_zscore < -threshold

            # Bollinger Band squeeze detection
            bb_upper, bb_middle, bb_lower = AdvancedTechnicalAnalyzer.calculate_bollinger_bands(data, period)
            if not bb_upper.empty and not bb_lower.empty:
                band_width = (bb_upper.iloc[-1] - bb_lower.iloc[-1]) / bb_middle.iloc[-1]
                band_width_ma = pd.Series([band_width]).rolling(window=min(10, len(data))).mean().iloc[-1]
                squeeze = band_width < band_width_ma * 0.8
            else:
                squeeze = False

            # Determine volatility state
            if breakout:
                volatility_state = 'high_breakout'
            elif contraction or squeeze:
                volatility_state = 'low_contraction'
            else:
                volatility_state = 'normal'

            return {
                'breakout': breakout,
                'contraction': contraction,
                'squeeze': squeeze,
                'volatility_state': volatility_state,
                'volatility_zscore': volatility_zscore,
                'current_atr': current_atr,
                'mean_atr': mean_atr
            }

        except:
            return {'breakout': False, 'contraction': False, 'volatility_state': 'normal'}

    @staticmethod
    def analyze_volatility_clustering(data: pd.DataFrame, period: int = 20) -> Dict[str, any]:
        """Analyze volatility clustering patterns"""
        try:
            if len(data) < period * 2:
                return {'clustering': False, 'cluster_strength': 0.0, 'persistence': 0.0}

            # Calculate returns
            returns = data['close'].pct_change().dropna()

            # Calculate rolling volatility
            rolling_vol = returns.rolling(window=period).std()

            # Detect volatility clusters (periods of high/low volatility persistence)
            vol_changes = rolling_vol.diff().abs()
            vol_persistence = 1.0 - (vol_changes.rolling(window=period).mean() / rolling_vol.rolling(window=period).mean())

            current_persistence = vol_persistence.iloc[-1] if not vol_persistence.empty else 0.0

            # Detect clustering
            clustering = current_persistence > 0.7
            cluster_strength = max(0.0, min(1.0, current_persistence))

            # Calculate GARCH-like volatility prediction
            recent_returns = returns.tail(period)
            if len(recent_returns) > 0:
                volatility_forecast = recent_returns.std() * np.sqrt(period)
            else:
                volatility_forecast = 0.0

            return {
                'clustering': clustering,
                'cluster_strength': cluster_strength,
                'persistence': current_persistence,
                'volatility_forecast': volatility_forecast,
                'current_volatility': rolling_vol.iloc[-1] if not rolling_vol.empty else 0.0
            }

        except:
            return {'clustering': False, 'cluster_strength': 0.0, 'persistence': 0.0}


class CycleAnalysis:
    """🔄 ADVANCED CYCLE ANALYSIS ENGINE 🔄"""

    @staticmethod
    def detect_dominant_cycle(data: pd.DataFrame, min_period: int = 8, max_period: int = 50) -> Dict[str, any]:
        """Detect dominant market cycle using spectral analysis"""
        try:
            if len(data) < max_period * 2:
                return {'dominant_cycle': 20, 'cycle_strength': 0.0, 'phase': 0.0}

            # Use closing prices
            prices = data['close'].values

            # Detrend the data
            detrended = prices - np.mean(prices)

            # Simple cycle detection using autocorrelation
            autocorr_values = []
            periods = range(min_period, min(max_period, len(prices) // 2))

            for period in periods:
                if len(detrended) > period:
                    # Calculate autocorrelation for this period
                    correlation = np.corrcoef(detrended[:-period], detrended[period:])[0, 1]
                    autocorr_values.append((period, abs(correlation) if not np.isnan(correlation) else 0))

            if not autocorr_values:
                return {'dominant_cycle': 20, 'cycle_strength': 0.0, 'phase': 0.0}

            # Find period with highest autocorrelation
            dominant_period, cycle_strength = max(autocorr_values, key=lambda x: x[1])

            # Calculate current phase (0-1, where 0 = cycle bottom, 0.5 = cycle top)
            if dominant_period > 0 and len(prices) >= dominant_period:
                recent_prices = prices[-dominant_period:]
                cycle_position = (len(recent_prices) - np.argmin(recent_prices)) / len(recent_prices)
                phase = cycle_position % 1.0
            else:
                phase = 0.0

            return {
                'dominant_cycle': dominant_period,
                'cycle_strength': cycle_strength,
                'phase': phase,
                'cycle_position': 'bottom' if phase < 0.25 else 'rising' if phase < 0.5 else 'top' if phase < 0.75 else 'falling'
            }

        except:
            return {'dominant_cycle': 20, 'cycle_strength': 0.0, 'phase': 0.0}

    @staticmethod
    def analyze_cycle_convergence(data: pd.DataFrame) -> Dict[str, any]:
        """Analyze convergence of multiple cycle periods"""
        try:
            if len(data) < 100:
                return {'convergence': False, 'convergence_strength': 0.0, 'cycles': []}

            # Detect multiple cycles
            cycles = []
            for min_p, max_p in [(8, 20), (20, 35), (35, 50)]:
                cycle_info = CycleAnalysis.detect_dominant_cycle(data, min_p, max_p)
                if cycle_info['cycle_strength'] > 0.3:
                    cycles.append(cycle_info)

            if len(cycles) < 2:
                return {'convergence': False, 'convergence_strength': 0.0, 'cycles': cycles}

            # Check for phase alignment
            phases = [cycle['phase'] for cycle in cycles]

            # Calculate phase convergence (all cycles in similar phase)
            phase_variance = np.var(phases)
            convergence_strength = max(0.0, 1.0 - phase_variance * 4)  # Scale variance to 0-1

            # Convergence occurs when multiple cycles are aligned
            convergence = convergence_strength > 0.6

            # Determine overall cycle direction
            avg_phase = np.mean(phases)
            if avg_phase < 0.3 or avg_phase > 0.7:
                cycle_direction = 'turning_point'  # Near cycle extremes
            elif 0.3 <= avg_phase <= 0.7:
                cycle_direction = 'trending'  # Mid-cycle
            else:
                cycle_direction = 'neutral'

            return {
                'convergence': convergence,
                'convergence_strength': convergence_strength,
                'cycles': cycles,
                'avg_phase': avg_phase,
                'cycle_direction': cycle_direction,
                'phase_variance': phase_variance
            }

        except:
            return {'convergence': False, 'convergence_strength': 0.0, 'cycles': []}

    @staticmethod
    def calculate_cycle_momentum(data: pd.DataFrame, cycle_period: int = 20) -> Dict[str, any]:
        """Calculate momentum within the dominant cycle"""
        try:
            if len(data) < cycle_period * 2:
                return {'cycle_momentum': 0.0, 'momentum_strength': 0.0, 'momentum_direction': 'neutral'}

            # Get cycle information
            cycle_info = CycleAnalysis.detect_dominant_cycle(data)
            dominant_period = cycle_info['dominant_cycle']

            # Calculate momentum using rate of change over cycle period
            prices = data['close']
            cycle_roc = (prices.iloc[-1] - prices.iloc[-dominant_period]) / prices.iloc[-dominant_period]

            # Calculate momentum strength using recent price action
            recent_prices = prices.tail(dominant_period // 2)
            momentum_strength = abs(recent_prices.pct_change().mean()) * 100

            # Determine momentum direction
            if cycle_roc > 0.01:  # 1% threshold
                momentum_direction = 'bullish'
            elif cycle_roc < -0.01:
                momentum_direction = 'bearish'
            else:
                momentum_direction = 'neutral'

            # Combine with cycle phase for enhanced signal
            phase = cycle_info['phase']
            if momentum_direction == 'bullish' and 0.0 <= phase <= 0.5:
                enhanced_signal = 'strong_bullish'  # Bullish momentum in rising phase
            elif momentum_direction == 'bearish' and 0.5 <= phase <= 1.0:
                enhanced_signal = 'strong_bearish'  # Bearish momentum in falling phase
            else:
                enhanced_signal = momentum_direction

            return {
                'cycle_momentum': cycle_roc,
                'momentum_strength': momentum_strength,
                'momentum_direction': momentum_direction,
                'enhanced_signal': enhanced_signal,
                'cycle_phase': phase,
                'dominant_period': dominant_period
            }

        except:
            return {'cycle_momentum': 0.0, 'momentum_strength': 0.0, 'momentum_direction': 'neutral'}


class MultiTimeframeAnalyzer:
    """📊 Multi-Timeframe Analysis Engine for Enhanced Signal Accuracy"""

    @staticmethod
    def resample_to_timeframe(data: pd.DataFrame, timeframe_minutes: int) -> pd.DataFrame:
        """Resample 1-minute data to higher timeframes"""
        try:
            if len(data) < timeframe_minutes:
                return None

            # Create time index if not exists
            if 'time' not in data.columns:
                data['time'] = pd.date_range(end=pd.Timestamp.now(), periods=len(data), freq='1min')

            # Set time as index
            df = data.copy()
            df['time'] = pd.to_datetime(df['time'])
            df.set_index('time', inplace=True)

            # Resample to target timeframe
            resampled = df.resample(f'{timeframe_minutes}min').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last'
            }).dropna()

            # Reset index and return
            resampled.reset_index(inplace=True)
            return resampled[['open', 'high', 'low', 'close']]

        except Exception as e:
            return None

    @staticmethod
    def analyze_timeframe_confluence(tf1_signal: Signal, tf1_conf: float,
                                   tf5_signal: Signal, tf5_conf: float,
                                   tf15_signal: Signal, tf15_conf: float) -> Tuple[Signal, float]:
        """Analyze confluence across multiple timeframes with weighted scoring"""

        # Timeframe weights (higher timeframes have more weight)
        weights = {
            '1m': 0.3,   # 30% weight for 1-minute
            '5m': 0.4,   # 40% weight for 5-minute
            '15m': 0.3   # 30% weight for 15-minute
        }

        # Convert signals to numeric values for calculation
        signal_values = {Signal.UP: 1, Signal.DOWN: -1, Signal.NEUTRAL: 0}

        tf1_value = signal_values[tf1_signal]
        tf5_value = signal_values[tf5_signal]
        tf15_value = signal_values[tf15_signal]

        # Calculate weighted signal strength
        weighted_signal = (
            tf1_value * tf1_conf * weights['1m'] +
            tf5_value * tf5_conf * weights['5m'] +
            tf15_value * tf15_conf * weights['15m']
        )

        # Calculate confluence confidence
        confluence_factors = []

        # Factor 1: Signal agreement
        signals = [tf1_signal, tf5_signal, tf15_signal]
        non_neutral = [s for s in signals if s != Signal.NEUTRAL]

        if len(non_neutral) >= 2:
            # Check if non-neutral signals agree
            if len(set(non_neutral)) == 1:  # All agree
                agreement_bonus = 0.3
            else:  # Mixed signals
                agreement_bonus = -0.2
        else:
            agreement_bonus = 0.0

        confluence_factors.append(agreement_bonus)

        # Factor 2: Higher timeframe dominance
        if tf15_signal != Signal.NEUTRAL and tf15_conf > 0.6:
            htf_bonus = 0.2  # Higher timeframe has strong signal
        elif tf5_signal != Signal.NEUTRAL and tf5_conf > 0.7:
            htf_bonus = 0.1  # Medium timeframe has strong signal
        else:
            htf_bonus = 0.0

        confluence_factors.append(htf_bonus)

        # Factor 3: Confidence alignment
        confidences = [tf1_conf, tf5_conf, tf15_conf]
        avg_confidence = sum(confidences) / len(confidences)
        confidence_bonus = (avg_confidence - 0.5) * 0.2  # Bonus for high avg confidence

        confluence_factors.append(confidence_bonus)

        # Calculate final confidence
        base_confidence = abs(weighted_signal)
        confluence_bonus = sum(confluence_factors)
        final_confidence = min(1.0, max(0.0, base_confidence + confluence_bonus))

        # Determine final signal
        if abs(weighted_signal) < 0.1:
            final_signal = Signal.NEUTRAL
        elif weighted_signal > 0:
            final_signal = Signal.UP
        else:
            final_signal = Signal.DOWN

        return final_signal, final_confidence

    @staticmethod
    def get_timeframe_analysis_summary(tf1_result: Dict, tf5_result: Dict, tf15_result: Dict) -> Dict:
        """Generate comprehensive timeframe analysis summary"""
        return {
            'timeframes': {
                '1m': {
                    'signal': tf1_result['signal'],
                    'confidence': tf1_result['confidence'],
                    'weight': 0.3
                },
                '5m': {
                    'signal': tf5_result['signal'],
                    'confidence': tf5_result['confidence'],
                    'weight': 0.4
                },
                '15m': {
                    'signal': tf15_result['signal'],
                    'confidence': tf15_result['confidence'],
                    'weight': 0.3
                }
            },
            'confluence_analysis': {
                'agreement_level': 'high' if len(set([tf1_result['signal'], tf5_result['signal'], tf15_result['signal']])) <= 2 else 'low',
                'dominant_timeframe': '15m' if tf15_result['confidence'] > 0.6 else ('5m' if tf5_result['confidence'] > 0.6 else '1m'),
                'signal_strength': 'strong' if max(tf1_result['confidence'], tf5_result['confidence'], tf15_result['confidence']) > 0.7 else 'moderate'
            }
        }


class IntelligentRuleBasedModel:
    """
    🧠 INTELLIGENT RULE-BASED TRADING MODEL WITH MULTI-TIMEFRAME ANALYSIS 🧠

    Advanced AI-like analysis using sophisticated price action and candle psychology.
    Enhanced with multi-timeframe confluence analysis for superior accuracy.

    Features:
    ✅ Multi-Timeframe Analysis (1m, 5m, 15m confluence)
    ✅ Market Structure Analysis (HH/HL, LH/LL, Shifts)
    ✅ Support/Resistance & Breakout Detection
    ✅ Advanced Candle Psychology & Patterns
    ✅ Supply/Demand Zone Analysis
    ✅ Momentum & Range Analysis
    ✅ Multi-factor Confidence Scoring
    ✅ Intelligent Expiry Time Prediction
    """

    def __init__(self, confidence_threshold: float = 0.65, enable_advanced_analysis: bool = True):
        """
        Initialize the enhanced intelligent rule-based model with advanced AI-like learning

        Args:
            confidence_threshold: Minimum confidence required for non-NEUTRAL signals (lowered for 60s/90s)
            enable_advanced_analysis: Enable advanced analysis (default True for enhanced accuracy)
        """
        self.confidence_threshold = confidence_threshold
        self.enable_advanced_analysis = enable_advanced_analysis  # 🚀 Default to True for max accuracy
        self.false_signal_filter = FalseSignalFilter()  # Use existing class
        self.mtf_analyzer = MultiTimeframeAnalyzer()    # Use existing class

        # 🧠 ENHANCED: AI-like Adaptive Learning System with Pattern Memory
        self.learning_engine = AdaptiveLearningEngine()  # Use existing class

        # 🔥 ENHANCED ANALYSIS WEIGHTS - Optimized for 60s/90s trading
        self.analysis_weights = {
            'market_structure': 3.5,      # ULTRA HIGH - trend structure critical for short-term
            'support_resistance': 3.2,    # ULTRA HIGH - key levels are everything
            'breakout_analysis': 3.0,     # VERY HIGH - breakouts drive 60s/90s profits
            'candle_psychology': 2.8,     # VERY HIGH - immediate price action signals
            'momentum_analysis': 2.5,     # HIGH - momentum confirmation essential
            'supply_demand': 2.2,         # HIGH - institutional zone analysis
            'pattern_recognition': 2.0,   # HIGH - pattern confluence
            'wick_psychology': 1.8,       # MEDIUM-HIGH - rejection signals
            'volume_analysis': 1.5,       # MEDIUM - volume confirmation
            'volatility_analysis': 1.2    # MEDIUM - volatility assessment
        }

        # 🎯 ENHANCED ACCURACY TRACKING
        self.accuracy_metrics = {
            'total_predictions': 0,
            'successful_predictions': 0,
            'pattern_success_rates': {},
            'timeframe_performance': {},
            'confidence_calibration': {},
            'recent_performance': []
        }

        # Initialize analyzers
        self.price_action = PriceActionAnalyzer()
        self.candle_psychology = CandlePsychologyAnalyzer()
        self.advanced_technical = AdvancedTechnicalAnalyzer()

    def _analyze_market_structure(self, data: pd.DataFrame) -> AnalysisResult:
        """🏗️ Analyze market structure for trend direction and strength"""
        try:
            structure = self.price_action.analyze_market_structure(data)

            if structure.trend == TrendDirection.UPTREND:
                signal = Signal.UP
                confidence = structure.strength
                # Boost confidence if recent structure shift supports trend
                if structure.recent_shift:
                    confidence = min(0.95, confidence * 1.2)
            elif structure.trend == TrendDirection.DOWNTREND:
                signal = Signal.DOWN
                confidence = structure.strength
                # Boost confidence if recent structure shift supports trend
                if structure.recent_shift:
                    confidence = min(0.95, confidence * 1.2)
            else:
                signal = Signal.NEUTRAL
                confidence = 0.3

            details = {
                'trend': structure.trend.value,
                'strength': structure.strength,
                'recent_shift': structure.recent_shift,
                'structure_points': len(structure.structure_points)
            }

            return AnalysisResult(
                signal=signal,
                confidence=confidence,
                weight=self.analysis_weights['market_structure'],
                name="Market Structure",
                details=details
            )

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, self.analysis_weights['market_structure'], "Market Structure", {'error': str(e)})

    def _analyze_support_resistance(self, data: pd.DataFrame) -> AnalysisResult:
        """🎯 Analyze support and resistance levels"""
        try:
            levels = self.price_action.find_support_resistance_levels(data)
            current_price = data['close'].iloc[-1]

            if not levels:
                return AnalysisResult(Signal.NEUTRAL, 0.2, self.analysis_weights['support_resistance'], "Support/Resistance")

            # Find closest levels
            closest_support = None
            closest_resistance = None

            for level in levels:
                if level.level_type == 'support' and level.price < current_price:
                    if closest_support is None or level.price > closest_support.price:
                        closest_support = level
                elif level.level_type == 'resistance' and level.price > current_price:
                    if closest_resistance is None or level.price < closest_resistance.price:
                        closest_resistance = level

            # Analyze price position relative to levels
            signal = Signal.NEUTRAL
            confidence = 0.3

            price_std = data['close'].std()

            if closest_support and closest_resistance:
                support_distance = (current_price - closest_support.price) / price_std
                resistance_distance = (closest_resistance.price - current_price) / price_std

                # Near support = potential bounce UP
                if support_distance < 1.0:
                    signal = Signal.UP
                    confidence = min(0.8, closest_support.strength * (1 - support_distance / 2))
                # Near resistance = potential rejection DOWN
                elif resistance_distance < 1.0:
                    signal = Signal.DOWN
                    confidence = min(0.8, closest_resistance.strength * (1 - resistance_distance / 2))

            details = {
                'total_levels': len(levels),
                'closest_support': closest_support.price if closest_support else None,
                'closest_resistance': closest_resistance.price if closest_resistance else None,
                'current_price': current_price
            }

            return AnalysisResult(
                signal=signal,
                confidence=confidence,
                weight=self.analysis_weights['support_resistance'],
                name="Support/Resistance",
                details=details
            )

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, self.analysis_weights['support_resistance'], "Support/Resistance", {'error': str(e)})

    def _analyze_breakouts(self, data: pd.DataFrame) -> AnalysisResult:
        """💥 Analyze breakouts and fakeouts"""
        try:
            levels = self.price_action.find_support_resistance_levels(data)
            has_breakout, breakout_type, strength = self.price_action.detect_breakout_or_fakeout(data, levels)

            if not has_breakout:
                return AnalysisResult(Signal.NEUTRAL, 0.1, self.analysis_weights['breakout_analysis'], "Breakout Analysis")

            if breakout_type == "breakout_up":
                signal = Signal.UP
                confidence = min(0.9, strength)
            elif breakout_type == "breakout_down":
                signal = Signal.DOWN
                confidence = min(0.9, strength)
            elif breakout_type == "fakeout_up":
                signal = Signal.UP
                confidence = 0.6
            elif breakout_type == "fakeout_down":
                signal = Signal.DOWN
                confidence = 0.6
            else:
                signal = Signal.NEUTRAL
                confidence = 0.2

            details = {
                'breakout_detected': has_breakout,
                'breakout_type': breakout_type,
                'strength': strength
            }

            return AnalysisResult(
                signal=signal,
                confidence=confidence,
                weight=self.analysis_weights['breakout_analysis'],
                name="Breakout Analysis",
                details=details
            )

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, self.analysis_weights['breakout_analysis'], "Breakout Analysis", {'error': str(e)})

    def _analyze_candle_psychology(self, data: pd.DataFrame) -> AnalysisResult:
        """🕯️ Analyze advanced candle psychology and patterns"""
        try:
            # Analyze current candle type
            current_candle = data.iloc[-1]
            candle_type = self.candle_psychology.analyze_candle_type(current_candle)

            # Analyze wick psychology
            wick_analysis = self.candle_psychology.analyze_wick_psychology(data)

            # Detect engulfing patterns
            has_engulfing, engulfing_type, engulfing_strength = self.candle_psychology.detect_engulfing_patterns(data)

            # Detect three soldiers/crows
            has_soldiers_crows, pattern_type, pattern_strength = self.candle_psychology.detect_three_soldiers_crows(data)

            # Analyze candle sequence
            sequence_analysis = self.candle_psychology.analyze_candle_sequence(data)

            # Calculate momentum from candles
            momentum = self.candle_psychology.calculate_momentum_from_candles(data)

            # NEW: Detect advanced reversal patterns
            reversal_patterns = self.candle_psychology.detect_advanced_reversal_patterns(data)

            # NEW: Detect advanced continuation patterns
            continuation_patterns = self.candle_psychology.detect_advanced_continuation_patterns(data)

            # NEW: Detect multi-candle patterns
            multi_candle_patterns = self.candle_psychology.detect_multi_candle_patterns(data)

            # NEW: Detect Doji variations
            doji_variations = self.candle_psychology.detect_doji_variations(data)

            # NEW: Detect Tweezer patterns
            tweezer_patterns = self.candle_psychology.detect_tweezer_patterns(data)

            # Combine all signals
            signal = Signal.NEUTRAL
            confidence = 0.3

            # Priority 1: Advanced multi-candle patterns (highest priority)
            if multi_candle_patterns['pattern'] != 'none' and multi_candle_patterns['strength'] > 0.7:
                if multi_candle_patterns['signal'] == 'up':
                    signal = Signal.UP
                    confidence = min(0.95, multi_candle_patterns['strength'])
                elif multi_candle_patterns['signal'] == 'down':
                    signal = Signal.DOWN
                    confidence = min(0.95, multi_candle_patterns['strength'])

            # Priority 2: Advanced reversal patterns
            elif reversal_patterns['pattern'] != 'none' and reversal_patterns['strength'] > 0.7:
                if reversal_patterns['signal'] == 'up':
                    signal = Signal.UP
                    confidence = min(0.9, reversal_patterns['strength'])
                elif reversal_patterns['signal'] == 'down':
                    signal = Signal.DOWN
                    confidence = min(0.9, reversal_patterns['strength'])

            # Priority 3: Advanced continuation patterns
            elif continuation_patterns['pattern'] != 'none' and continuation_patterns['strength'] > 0.8:
                if continuation_patterns['signal'] == 'up':
                    signal = Signal.UP
                    confidence = min(0.88, continuation_patterns['strength'])
                elif continuation_patterns['signal'] == 'down':
                    signal = Signal.DOWN
                    confidence = min(0.88, continuation_patterns['strength'])

            # Priority 4: Tweezer patterns (high priority reversal)
            elif tweezer_patterns['pattern'] != 'none' and tweezer_patterns['strength'] > 0.8:
                if tweezer_patterns['signal'] == 'up':
                    signal = Signal.UP
                    confidence = min(0.92, tweezer_patterns['strength'])
                elif tweezer_patterns['signal'] == 'down':
                    signal = Signal.DOWN
                    confidence = min(0.92, tweezer_patterns['strength'])

            # Priority 5: Doji variations (strong reversal/indecision)
            elif doji_variations['pattern'] != 'none' and doji_variations['strength'] > 0.7:
                if doji_variations['signal'] == 'up':
                    signal = Signal.UP
                    confidence = min(0.88, doji_variations['strength'])
                elif doji_variations['signal'] == 'down':
                    signal = Signal.DOWN
                    confidence = min(0.88, doji_variations['strength'])
                elif doji_variations['signal'] == 'neutral':
                    # Dojis indicate indecision, can reduce confidence of existing signal
                    confidence = min(confidence, doji_variations['strength']) # Cap confidence at doji strength
                    if confidence < 0.5: # If very low, make neutral
                        signal = Signal.NEUTRAL

            # Priority 6: Traditional engulfing patterns
            elif has_engulfing and engulfing_strength > 0.7:
                if engulfing_type == "bullish_engulfing":
                    signal = Signal.UP
                    confidence = min(0.85, engulfing_strength)
                elif engulfing_type == "bearish_engulfing":
                    signal = Signal.DOWN
                    confidence = min(0.85, engulfing_strength)

            # Priority 6: Three soldiers/crows patterns
            elif has_soldiers_crows and pattern_strength > 0.6:
                if pattern_type == "three_white_soldiers":
                    signal = Signal.UP
                    confidence = min(0.8, pattern_strength)
                elif pattern_type == "three_black_crows":
                    signal = Signal.DOWN
                    confidence = min(0.8, pattern_strength)

            # Candle type signals
            elif candle_type in [CandleType.MARUBOZU_BULL, CandleType.PIN_BAR_BULL]:
                signal = Signal.UP
                confidence = 0.7
            elif candle_type in [CandleType.MARUBOZU_BEAR, CandleType.PIN_BAR_BEAR]:
                signal = Signal.DOWN
                confidence = 0.7
            elif candle_type == CandleType.HAMMER:
                signal = Signal.UP
                confidence = 0.6
            elif candle_type == CandleType.SHOOTING_STAR:
                signal = Signal.DOWN
                confidence = 0.6

            # Wick psychology adjustment
            if wick_analysis['buyer_strength'] > 0.7:
                if signal == Signal.UP:
                    confidence = min(0.95, confidence * 1.2)
                elif signal == Signal.NEUTRAL:
                    signal = Signal.UP
                    confidence = 0.5
            elif wick_analysis['seller_strength'] > 0.7:
                if signal == Signal.DOWN:
                    confidence = min(0.95, confidence * 1.2)
                elif signal == Signal.NEUTRAL:
                    signal = Signal.DOWN
                    confidence = 0.5

            # Sequence analysis adjustment
            if sequence_analysis['signal'] != 'neutral' and sequence_analysis['strength'] > 0.6:
                if sequence_analysis['signal'] == 'up':
                    if signal == Signal.UP:
                        confidence = min(0.95, confidence * 1.1)
                    elif signal == Signal.NEUTRAL:
                        signal = Signal.UP
                        confidence = sequence_analysis['strength']
                elif sequence_analysis['signal'] == 'down':
                    if signal == Signal.DOWN:
                        confidence = min(0.95, confidence * 1.1)
                    elif signal == Signal.NEUTRAL:
                        signal = Signal.DOWN
                        confidence = sequence_analysis['strength']

            details = {
                'candle_type': candle_type.value,
                'wick_analysis': wick_analysis,
                'engulfing_pattern': {'detected': has_engulfing, 'type': engulfing_type, 'strength': engulfing_strength},
                'soldiers_crows': {'detected': has_soldiers_crows, 'type': pattern_type, 'strength': pattern_strength},
                'sequence': sequence_analysis,
                'momentum': momentum,
                'advanced_reversal_patterns': reversal_patterns,
                'advanced_continuation_patterns': continuation_patterns,
                'advanced_multi_candle_patterns': multi_candle_patterns,
                'doji_variations': doji_variations,
                'tweezer_patterns': tweezer_patterns
            }

            return AnalysisResult(
                signal=signal,
                confidence=confidence,
                weight=self.analysis_weights['candle_psychology'],
                name="Candle Psychology",
                details=details
            )

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, self.analysis_weights['candle_psychology'], "Candle Psychology", {'error': str(e)})

    def _analyze_supply_demand_zones(self, data: pd.DataFrame) -> AnalysisResult:
        """📦 Analyze supply and demand zones"""
        try:
            zones = self.price_action.analyze_supply_demand_zones(data)
            current_price = data['close'].iloc[-1]

            if not zones:
                return AnalysisResult(Signal.NEUTRAL, 0.2, self.analysis_weights['supply_demand'], "Supply/Demand Zones")

            # Find relevant zones near current price
            price_std = data['close'].std()
            relevant_zones = []

            for zone in zones:
                distance = abs(zone['price'] - current_price)
                if distance <= price_std * 2:  # Within 2 standard deviations
                    relevant_zones.append(zone)

            if not relevant_zones:
                return AnalysisResult(Signal.NEUTRAL, 0.3, self.analysis_weights['supply_demand'], "Supply/Demand Zones")

            # Analyze strongest zone
            strongest_zone = max(relevant_zones, key=lambda x: x['strength'])

            signal = Signal.NEUTRAL
            confidence = 0.4

            if strongest_zone['type'] == 'demand' and current_price <= strongest_zone['price'] * 1.01:
                # Near demand zone = potential UP
                signal = Signal.UP
                confidence = min(0.8, strongest_zone['strength'] * 10)  # Scale strength
            elif strongest_zone['type'] == 'supply' and current_price >= strongest_zone['price'] * 0.99:
                # Near supply zone = potential DOWN
                signal = Signal.DOWN
                confidence = min(0.8, strongest_zone['strength'] * 10)  # Scale strength

            details = {
                'total_zones': len(zones),
                'relevant_zones': len(relevant_zones),
                'strongest_zone': strongest_zone,
                'current_price': current_price
            }

            return AnalysisResult(
                signal=signal,
                confidence=confidence,
                weight=self.analysis_weights['supply_demand'],
                name="Supply/Demand Zones",
                details=details
            )

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, self.analysis_weights['supply_demand'], "Supply/Demand Zones", {'error': str(e)})

    def _analyze_momentum(self, data: pd.DataFrame) -> AnalysisResult:
        """⚡ Analyze momentum and range expansion/contraction"""
        try:
            momentum = self.candle_psychology.calculate_momentum_from_candles(data)

            # Calculate additional momentum indicators
            if len(data) < 10:
                return AnalysisResult(Signal.NEUTRAL, 0.2, self.analysis_weights['momentum_analysis'], "Momentum Analysis")

            recent_data = data.tail(10)

            # Price momentum
            price_change = (recent_data['close'].iloc[-1] - recent_data['close'].iloc[0]) / recent_data['close'].iloc[0]

            # Volume-like momentum (using range as proxy since no volume data)
            ranges = [(candle['high'] - candle['low']) for _, candle in recent_data.iterrows()]
            avg_range = sum(ranges) / len(ranges)
            current_range = ranges[-1]
            range_momentum = (current_range - avg_range) / avg_range if avg_range > 0 else 0

            # Combine momentum signals
            total_momentum = (
                momentum['range_expansion'] * 0.3 +
                momentum['body_momentum'] * 0.4 +
                momentum['wick_momentum'] * 0.2 +
                price_change * 100 * 0.1  # Scale price change
            )

            signal = Signal.NEUTRAL
            confidence = 0.3

            if total_momentum > 0.3:
                signal = Signal.UP
                confidence = min(0.8, abs(total_momentum))
            elif total_momentum < -0.3:
                signal = Signal.DOWN
                confidence = min(0.8, abs(total_momentum))

            # Range expansion boost
            if range_momentum > 0.5:  # Strong range expansion
                confidence = min(0.9, confidence * 1.3)

            details = {
                'candle_momentum': momentum,
                'price_change': price_change,
                'range_momentum': range_momentum,
                'total_momentum': total_momentum
            }

            return AnalysisResult(
                signal=signal,
                confidence=confidence,
                weight=self.analysis_weights['momentum_analysis'],
                name="Momentum Analysis",
                details=details
            )

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, self.analysis_weights['momentum_analysis'], "Momentum Analysis", {'error': str(e)})

    def _analyze_advanced_price_patterns(self, data: pd.DataFrame) -> AnalysisResult:
        """🎯 Analyze advanced price action patterns (Flags, Triangles, etc.)"""
        try:
            # Detect advanced price patterns
            pattern_analysis = self.price_action.detect_advanced_price_patterns(data)

            if pattern_analysis['pattern'] == 'none':
                return AnalysisResult(Signal.NEUTRAL, 0.2, self.analysis_weights.get('pattern_recognition', 1.0), "Advanced Price Patterns")

            # Convert signal string to Signal enum
            if pattern_analysis['signal'] == 'up':
                signal = Signal.UP
            elif pattern_analysis['signal'] == 'down':
                signal = Signal.DOWN
            else:
                signal = Signal.NEUTRAL

            confidence = pattern_analysis['strength']

            # Boost confidence for high-probability patterns
            if pattern_analysis['pattern'] in ['bull_flag', 'bear_flag', 'ascending_triangle', 'descending_triangle']:
                confidence = min(0.9, confidence * 1.1)

            details = {
                'pattern_detected': pattern_analysis['pattern'],
                'pattern_strength': pattern_analysis['strength'],
                'pattern_description': pattern_analysis.get('description', 'Advanced price pattern detected')
            }

            return AnalysisResult(
                signal=signal,
                confidence=confidence,
                weight=self.analysis_weights.get('pattern_recognition', 1.0),
                name="Advanced Price Patterns",
                details=details
            )

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, self.analysis_weights.get('pattern_recognition', 1.0), "Advanced Price Patterns", {'error': str(e)})

    def _analyze_advanced_technical_indicators(self, data: pd.DataFrame) -> AnalysisResult:
        """🚀 FAST TECHNICAL INDICATORS ANALYSIS 🚀"""
        try:
            if len(data) < 15:  # Reduced minimum data requirement
                return AnalysisResult(Signal.NEUTRAL, 0.0, 1.0, "fast_technical",
                                    {'analysis': 'insufficient_data'})

            signals = []
            confidences = []
            details = {}

            # 🚀 OPTIMIZED: Only calculate essential indicators for speed

            # 1. Fast RSI Analysis (reduced period for speed)
            try:
                rsi = self.advanced_technical.calculate_advanced_rsi(data, period=10)  # Faster with period 10
                if not rsi.empty and not rsi.isna().all():
                    current_rsi = rsi.iloc[-1]
                    details['rsi'] = current_rsi

                    if current_rsi < 25:  # Oversold
                        signals.append(Signal.UP)
                        confidences.append(0.75)
                        details['rsi_signal'] = 'oversold_bullish'
                    elif current_rsi > 75:  # Overbought
                        signals.append(Signal.DOWN)
                        confidences.append(0.75)
                        details['rsi_signal'] = 'overbought_bearish'
            except:
                pass

            # 2. Fast Bollinger Bands Analysis (reduced period)
            try:
                bb_upper, bb_middle, bb_lower = self.advanced_technical.calculate_bollinger_bands(data, period=10)
                if not bb_upper.empty and not bb_lower.empty:
                    current_price = data['close'].iloc[-1]
                    upper_val = bb_upper.iloc[-1]
                    lower_val = bb_lower.iloc[-1]

                    details['bollinger_position'] = 'above_upper' if current_price > upper_val else 'below_lower' if current_price < lower_val else 'middle'

                    if current_price <= lower_val:  # Price at lower band
                        signals.append(Signal.UP)
                        confidences.append(0.65)
                        details['bb_signal'] = 'bounce_from_lower'
                    elif current_price >= upper_val:  # Price at upper band
                        signals.append(Signal.DOWN)
                        confidences.append(0.65)
                        details['bb_signal'] = 'rejection_from_upper'
            except:
                pass

            # 🚀 SKIP HEAVY CALCULATIONS FOR SPEED
            # Removed: MACD, Williams %R, CCI, ADX, Divergence analysis
            # These are computationally expensive and slow down the analysis

            # Combine all signals
            if not signals:
                return AnalysisResult(Signal.NEUTRAL, 0.0, 1.0, "advanced_technical", details)

            # Calculate weighted average
            up_signals = [i for i, s in enumerate(signals) if s == Signal.UP]
            down_signals = [i for i, s in enumerate(signals) if s == Signal.DOWN]

            if len(up_signals) > len(down_signals):
                avg_confidence = np.mean([confidences[i] for i in up_signals])
                signal_strength = len(up_signals) / len(signals)
                final_confidence = avg_confidence * signal_strength
                final_signal = Signal.UP
            elif len(down_signals) > len(up_signals):
                avg_confidence = np.mean([confidences[i] for i in down_signals])
                signal_strength = len(down_signals) / len(signals)
                final_confidence = avg_confidence * signal_strength
                final_signal = Signal.DOWN
            else:
                final_signal = Signal.NEUTRAL
                final_confidence = 0.0

            details['signal_count'] = {
                'total': len(signals),
                'up': len(up_signals),
                'down': len(down_signals)
            }

            return AnalysisResult(final_signal, final_confidence, 1.5, "advanced_technical", details)

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, 1.0, "advanced_technical",
                                {'error': str(e), 'analysis': 'failed'})

    def _analyze_enhanced_mathematical_indicators(self, data: pd.DataFrame) -> AnalysisResult:
        """🧮 ENHANCED MATHEMATICAL INDICATORS ANALYSIS 🧮"""
        try:
            if len(data) < 30:
                return AnalysisResult(Signal.NEUTRAL, 0.0, 1.2, "enhanced_mathematical",
                                    {'analysis': 'insufficient_data'})

            signals = []
            confidences = []
            details = {}
            math_indicators = AdvancedMathematicalIndicators()

            # 1. KAMA Analysis
            try:
                kama = math_indicators.calculate_kama(data, period=14)
                if not kama.empty:
                    current_price = data['close'].iloc[-1]
                    kama_val = kama.iloc[-1]
                    kama_trend = kama.iloc[-1] - kama.iloc[-2] if len(kama) > 1 else 0

                    details['kama'] = {'value': kama_val, 'trend': kama_trend}

                    # KAMA signals
                    if current_price > kama_val and kama_trend > 0:
                        signals.append(Signal.UP)
                        confidences.append(0.75)
                        details['kama_signal'] = 'bullish_above_rising'
                    elif current_price < kama_val and kama_trend < 0:
                        signals.append(Signal.DOWN)
                        confidences.append(0.75)
                        details['kama_signal'] = 'bearish_below_falling'
            except:
                pass

            # 2. ZLEMA Analysis
            try:
                zlema = math_indicators.calculate_zlema(data, period=14)
                if not zlema.empty:
                    current_price = data['close'].iloc[-1]
                    zlema_val = zlema.iloc[-1]
                    zlema_trend = zlema.iloc[-1] - zlema.iloc[-2] if len(zlema) > 1 else 0

                    details['zlema'] = {'value': zlema_val, 'trend': zlema_trend}

                    # ZLEMA signals (zero lag = faster signals)
                    if current_price > zlema_val and zlema_trend > 0:
                        signals.append(Signal.UP)
                        confidences.append(0.70)
                        details['zlema_signal'] = 'bullish_crossover'
                    elif current_price < zlema_val and zlema_trend < 0:
                        signals.append(Signal.DOWN)
                        confidences.append(0.70)
                        details['zlema_signal'] = 'bearish_crossover'
            except:
                pass

            # 3. FRAMA Analysis
            try:
                frama = math_indicators.calculate_frama(data, period=14)
                if not frama.empty:
                    current_price = data['close'].iloc[-1]
                    frama_val = frama.iloc[-1]

                    # FRAMA adapts to market fractality
                    price_distance = abs(current_price - frama_val) / current_price
                    details['frama'] = {'value': frama_val, 'distance': price_distance}

                    if current_price > frama_val and price_distance < 0.01:  # Close to FRAMA
                        signals.append(Signal.UP)
                        confidences.append(0.65)
                        details['frama_signal'] = 'bullish_near_frama'
                    elif current_price < frama_val and price_distance < 0.01:
                        signals.append(Signal.DOWN)
                        confidences.append(0.65)
                        details['frama_signal'] = 'bearish_near_frama'
            except:
                pass

            # 4. Jurik MA Analysis
            try:
                jma = math_indicators.calculate_jurik_ma(data, period=14)
                if not jma.empty:
                    current_price = data['close'].iloc[-1]
                    jma_val = jma.iloc[-1]
                    jma_trend = jma.iloc[-1] - jma.iloc[-2] if len(jma) > 1 else 0

                    details['jurik_ma'] = {'value': jma_val, 'trend': jma_trend}

                    # Jurik MA signals (superior smoothing)
                    if current_price > jma_val and jma_trend > 0:
                        signals.append(Signal.UP)
                        confidences.append(0.68)
                        details['jma_signal'] = 'bullish_trend'
                    elif current_price < jma_val and jma_trend < 0:
                        signals.append(Signal.DOWN)
                        confidences.append(0.68)
                        details['jma_signal'] = 'bearish_trend'
            except:
                pass

            # Combine signals
            if not signals:
                return AnalysisResult(Signal.NEUTRAL, 0.0, 1.2, "enhanced_mathematical", details)

            # Calculate weighted average
            up_signals = [i for i, s in enumerate(signals) if s == Signal.UP]
            down_signals = [i for i, s in enumerate(signals) if s == Signal.DOWN]

            if len(up_signals) > len(down_signals):
                avg_confidence = np.mean([confidences[i] for i in up_signals])
                signal_strength = len(up_signals) / len(signals)
                final_confidence = avg_confidence * signal_strength
                final_signal = Signal.UP
            elif len(down_signals) > len(up_signals):
                avg_confidence = np.mean([confidences[i] for i in down_signals])
                signal_strength = len(down_signals) / len(signals)
                final_confidence = avg_confidence * signal_strength
                final_signal = Signal.DOWN
            else:
                final_signal = Signal.NEUTRAL
                final_confidence = 0.0

            details['signal_count'] = {
                'total': len(signals),
                'up': len(up_signals),
                'down': len(down_signals)
            }

            return AnalysisResult(final_signal, final_confidence, 1.2, "enhanced_mathematical", details)

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, 1.2, "enhanced_mathematical",
                                {'error': str(e), 'analysis': 'failed'})

    def _analyze_enhanced_pattern_recognition(self, data: pd.DataFrame) -> AnalysisResult:
        """🎯 ENHANCED PATTERN RECOGNITION ANALYSIS 🎯"""
        try:
            if len(data) < 20:
                return AnalysisResult(Signal.NEUTRAL, 0.0, 1.3, "enhanced_patterns",
                                    {'analysis': 'insufficient_data'})

            signals = []
            confidences = []
            details = {}
            pattern_engine = EnhancedPatternRecognition()

            # 1. Harmonic Patterns Analysis
            try:
                harmonic = pattern_engine.detect_harmonic_patterns(data)
                if harmonic['pattern']:
                    details['harmonic_pattern'] = harmonic

                    if harmonic['type'] == 'bullish':
                        signals.append(Signal.UP)
                        confidences.append(harmonic['confidence'])
                        details['harmonic_signal'] = f"bullish_{harmonic['pattern'].lower()}"
                    elif harmonic['type'] == 'bearish':
                        signals.append(Signal.DOWN)
                        confidences.append(harmonic['confidence'])
                        details['harmonic_signal'] = f"bearish_{harmonic['pattern'].lower()}"
            except:
                pass

            # 2. Advanced Candlestick Combinations
            try:
                candlestick = pattern_engine.detect_advanced_candlestick_combinations(data)
                if candlestick['pattern']:
                    details['candlestick_pattern'] = candlestick

                    if candlestick['signal'] == 'UP':
                        signals.append(Signal.UP)
                        confidences.append(candlestick['confidence'])
                        details['candlestick_signal'] = f"bullish_{candlestick['pattern'].lower().replace(' ', '_')}"
                    elif candlestick['signal'] == 'DOWN':
                        signals.append(Signal.DOWN)
                        confidences.append(candlestick['confidence'])
                        details['candlestick_signal'] = f"bearish_{candlestick['pattern'].lower().replace(' ', '_')}"
            except:
                pass

            # Combine signals
            if not signals:
                return AnalysisResult(Signal.NEUTRAL, 0.0, 1.3, "enhanced_patterns", details)

            # Calculate weighted average
            up_signals = [i for i, s in enumerate(signals) if s == Signal.UP]
            down_signals = [i for i, s in enumerate(signals) if s == Signal.DOWN]

            if len(up_signals) > len(down_signals):
                avg_confidence = np.mean([confidences[i] for i in up_signals])
                signal_strength = len(up_signals) / len(signals)
                final_confidence = avg_confidence * signal_strength
                final_signal = Signal.UP
            elif len(down_signals) > len(up_signals):
                avg_confidence = np.mean([confidences[i] for i in down_signals])
                signal_strength = len(down_signals) / len(signals)
                final_confidence = avg_confidence * signal_strength
                final_signal = Signal.DOWN
            else:
                final_signal = Signal.NEUTRAL
                final_confidence = 0.0

            details['signal_count'] = {
                'total': len(signals),
                'up': len(up_signals),
                'down': len(down_signals)
            }

            return AnalysisResult(final_signal, final_confidence, 1.3, "enhanced_patterns", details)

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, 1.3, "enhanced_patterns",
                                {'error': str(e), 'analysis': 'failed'})

    def _analyze_advanced_volatility(self, data: pd.DataFrame) -> AnalysisResult:
        """📊 ADVANCED VOLATILITY ANALYSIS 📊"""
        try:
            if len(data) < 25:
                return AnalysisResult(Signal.NEUTRAL, 0.0, 1.1, "advanced_volatility",
                                    {'analysis': 'insufficient_data'})

            signals = []
            confidences = []
            details = {}
            vol_analyzer = AdvancedVolatilityAnalysis()

            # 1. Volatility Breakouts Analysis
            try:
                breakouts = vol_analyzer.detect_volatility_breakouts(data)
                details['volatility_breakouts'] = breakouts

                if breakouts['breakout']:
                    # High volatility breakout - trend continuation likely
                    signals.append(Signal.UP if data['close'].iloc[-1] > data['close'].iloc[-2] else Signal.DOWN)
                    confidences.append(0.70)
                    details['volatility_signal'] = 'breakout_continuation'
                elif breakouts['contraction']:
                    # Low volatility - potential breakout coming
                    signals.append(Signal.NEUTRAL)  # Wait for direction
                    confidences.append(0.30)
                    details['volatility_signal'] = 'contraction_pending'
            except:
                pass

            # 2. Volatility Clustering Analysis
            try:
                clustering = vol_analyzer.analyze_volatility_clustering(data)
                details['volatility_clustering'] = clustering

                if clustering['clustering'] and clustering['cluster_strength'] > 0.7:
                    # Strong volatility clustering - trend likely to continue
                    price_direction = 1 if data['close'].iloc[-1] > data['close'].iloc[-5] else -1
                    if price_direction > 0:
                        signals.append(Signal.UP)
                        confidences.append(0.65)
                        details['clustering_signal'] = 'bullish_cluster'
                    else:
                        signals.append(Signal.DOWN)
                        confidences.append(0.65)
                        details['clustering_signal'] = 'bearish_cluster'
            except:
                pass

            # Combine signals
            if not signals:
                return AnalysisResult(Signal.NEUTRAL, 0.0, 1.1, "advanced_volatility", details)

            # Calculate weighted average
            up_signals = [i for i, s in enumerate(signals) if s == Signal.UP]
            down_signals = [i for i, s in enumerate(signals) if s == Signal.DOWN]
            neutral_signals = [i for i, s in enumerate(signals) if s == Signal.NEUTRAL]

            if len(up_signals) > len(down_signals) and len(up_signals) > len(neutral_signals):
                avg_confidence = np.mean([confidences[i] for i in up_signals])
                final_signal = Signal.UP
                final_confidence = avg_confidence
            elif len(down_signals) > len(up_signals) and len(down_signals) > len(neutral_signals):
                avg_confidence = np.mean([confidences[i] for i in down_signals])
                final_signal = Signal.DOWN
                final_confidence = avg_confidence
            else:
                final_signal = Signal.NEUTRAL
                final_confidence = np.mean(confidences) if confidences else 0.0

            details['signal_count'] = {
                'total': len(signals),
                'up': len(up_signals),
                'down': len(down_signals),
                'neutral': len(neutral_signals)
            }

            return AnalysisResult(final_signal, final_confidence, 1.1, "advanced_volatility", details)

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, 1.1, "advanced_volatility",
                                {'error': str(e), 'analysis': 'failed'})

    def _analyze_cycle_analysis(self, data: pd.DataFrame) -> AnalysisResult:
        """🔄 ADVANCED CYCLE ANALYSIS 🔄"""
        try:
            if len(data) < 50:
                return AnalysisResult(Signal.NEUTRAL, 0.0, 1.0, "cycle_analysis",
                                    {'analysis': 'insufficient_data'})

            signals = []
            confidences = []
            details = {}
            cycle_analyzer = CycleAnalysis()

            # 1. Dominant Cycle Detection
            try:
                dominant_cycle = cycle_analyzer.detect_dominant_cycle(data)
                details['dominant_cycle'] = dominant_cycle

                if dominant_cycle['cycle_strength'] > 0.5:
                    phase = dominant_cycle['phase']
                    cycle_position = dominant_cycle['cycle_position']

                    # Cycle-based signals
                    if cycle_position == 'bottom' or cycle_position == 'rising':
                        signals.append(Signal.UP)
                        confidences.append(0.65 * dominant_cycle['cycle_strength'])
                        details['cycle_signal'] = f"bullish_{cycle_position}"
                    elif cycle_position == 'top' or cycle_position == 'falling':
                        signals.append(Signal.DOWN)
                        confidences.append(0.65 * dominant_cycle['cycle_strength'])
                        details['cycle_signal'] = f"bearish_{cycle_position}"
            except:
                pass

            # 2. Cycle Convergence Analysis
            try:
                convergence = cycle_analyzer.analyze_cycle_convergence(data)
                details['cycle_convergence'] = convergence

                if convergence['convergence'] and convergence['convergence_strength'] > 0.6:
                    cycle_direction = convergence['cycle_direction']

                    if cycle_direction == 'turning_point':
                        # At cycle extremes - potential reversal
                        avg_phase = convergence['avg_phase']
                        if avg_phase < 0.3:  # Near bottom
                            signals.append(Signal.UP)
                            confidences.append(0.70)
                            details['convergence_signal'] = 'bullish_turning_point'
                        elif avg_phase > 0.7:  # Near top
                            signals.append(Signal.DOWN)
                            confidences.append(0.70)
                            details['convergence_signal'] = 'bearish_turning_point'
                    elif cycle_direction == 'trending':
                        # Mid-cycle - trend continuation
                        price_trend = 1 if data['close'].iloc[-1] > data['close'].iloc[-10] else -1
                        if price_trend > 0:
                            signals.append(Signal.UP)
                            confidences.append(0.60)
                            details['convergence_signal'] = 'bullish_trending'
                        else:
                            signals.append(Signal.DOWN)
                            confidences.append(0.60)
                            details['convergence_signal'] = 'bearish_trending'
            except:
                pass

            # 3. Cycle Momentum Analysis
            try:
                momentum = cycle_analyzer.calculate_cycle_momentum(data)
                details['cycle_momentum'] = momentum

                enhanced_signal = momentum['enhanced_signal']
                momentum_strength = momentum['momentum_strength']

                if enhanced_signal == 'strong_bullish':
                    signals.append(Signal.UP)
                    confidences.append(0.75)
                    details['momentum_signal'] = 'strong_bullish_momentum'
                elif enhanced_signal == 'strong_bearish':
                    signals.append(Signal.DOWN)
                    confidences.append(0.75)
                    details['momentum_signal'] = 'strong_bearish_momentum'
                elif enhanced_signal == 'bullish' and momentum_strength > 0.5:
                    signals.append(Signal.UP)
                    confidences.append(0.60)
                    details['momentum_signal'] = 'bullish_momentum'
                elif enhanced_signal == 'bearish' and momentum_strength > 0.5:
                    signals.append(Signal.DOWN)
                    confidences.append(0.60)
                    details['momentum_signal'] = 'bearish_momentum'
            except:
                pass

            # Combine signals
            if not signals:
                return AnalysisResult(Signal.NEUTRAL, 0.0, 1.0, "cycle_analysis", details)

            # Calculate weighted average
            up_signals = [i for i, s in enumerate(signals) if s == Signal.UP]
            down_signals = [i for i, s in enumerate(signals) if s == Signal.DOWN]

            if len(up_signals) > len(down_signals):
                avg_confidence = np.mean([confidences[i] for i in up_signals])
                signal_strength = len(up_signals) / len(signals)
                final_confidence = avg_confidence * signal_strength
                final_signal = Signal.UP
            elif len(down_signals) > len(up_signals):
                avg_confidence = np.mean([confidences[i] for i in down_signals])
                signal_strength = len(down_signals) / len(signals)
                final_confidence = avg_confidence * signal_strength
                final_signal = Signal.DOWN
            else:
                final_signal = Signal.NEUTRAL
                final_confidence = 0.0

            details['signal_count'] = {
                'total': len(signals),
                'up': len(up_signals),
                'down': len(down_signals)
            }

            return AnalysisResult(final_signal, final_confidence, 1.0, "cycle_analysis", details)

        except Exception as e:
            return AnalysisResult(Signal.NEUTRAL, 0.0, 1.0, "cycle_analysis",
                                {'error': str(e), 'analysis': 'failed'})

    def _analyze_single_timeframe(self, data: pd.DataFrame) -> Dict[str, any]:
        """Analyze a single timeframe and return signal/confidence"""
        if len(data) < 15:  # Reduced from 30 to 15 for faster analysis
            return {
                'signal': Signal.NEUTRAL.value,
                'confidence': 0.0,
                'details': {'error': 'Insufficient data for analysis'}
            }

        # Validate required columns
        required_columns = ['open', 'high', 'low', 'close']
        if not all(col in data.columns for col in required_columns):
            return {
                'signal': Signal.NEUTRAL.value,
                'confidence': 0.0,
                'details': {'error': f'Missing required columns: {required_columns}'}
            }

        try:
            # 🚀 CONFIGURABLE ANALYSIS - Speed vs Accuracy
            if self.enable_advanced_analysis:
                # FULL ADVANCED ANALYSIS (slower but more accurate)
                analyses = [
                    self._analyze_market_structure(data),
                    self._analyze_support_resistance(data),
                    self._analyze_breakouts(data),
                    self._analyze_candle_psychology(data),
                    self._analyze_supply_demand_zones(data),
                    self._analyze_momentum(data),
                    self._analyze_advanced_price_patterns(data),
                    self._analyze_advanced_technical_indicators(data),
                    self._analyze_enhanced_mathematical_indicators(data),
                    self._analyze_enhanced_pattern_recognition(data),
                    self._analyze_advanced_volatility(data),
                    self._analyze_cycle_analysis(data)
                ]
            else:
                # FAST ANALYSIS (optimized for speed)
                analyses = [
                    self._analyze_market_structure(data),
                    self._analyze_support_resistance(data),
                    self._analyze_breakouts(data),
                    self._analyze_candle_psychology(data),
                    self._analyze_momentum(data),
                    self._analyze_advanced_technical_indicators(data)  # Core technical indicators only
                ]

            # Calculate weighted scores
            up_score = 0.0
            down_score = 0.0
            neutral_score = 0.0
            total_weight = 0.0

            for analysis in analyses:
                weight = analysis.weight
                confidence = analysis.confidence
                weighted_confidence = confidence * weight

                if analysis.signal == Signal.UP:
                    up_score += weighted_confidence
                elif analysis.signal == Signal.DOWN:
                    down_score += weighted_confidence
                else:
                    neutral_score += weighted_confidence

                total_weight += weight

            # Normalize scores
            if total_weight > 0:
                up_score /= total_weight
                down_score /= total_weight
                neutral_score /= total_weight

            # Determine signal
            max_score = max(up_score, down_score, neutral_score)

            if max_score < 0.3:  # Lower threshold for individual timeframes
                final_signal = Signal.NEUTRAL
                final_confidence = max_score
            elif up_score == max_score and up_score > down_score * 1.1:
                final_signal = Signal.UP
                final_confidence = up_score
            elif down_score == max_score and down_score > up_score * 1.1:
                final_signal = Signal.DOWN
                final_confidence = down_score
            else:
                final_signal = Signal.NEUTRAL
                final_confidence = max_score * 0.7

            return {
                'signal': final_signal.value,
                'confidence': round(final_confidence, 4),
                'details': {
                    'up_score': round(up_score, 4),
                    'down_score': round(down_score, 4),
                    'neutral_score': round(neutral_score, 4)
                }
            }

        except Exception as e:
            return {
                'signal': Signal.NEUTRAL.value,
                'confidence': 0.0,
                'details': {'error': f'Analysis failed: {str(e)}'}
            }

    def predict_multi_timeframe(self, data: pd.DataFrame) -> Dict[str, any]:
        """
        🧠📊 MULTI-TIMEFRAME PREDICTION ENGINE 📊🧠

        Generate enhanced trading predictions using multi-timeframe confluence analysis.
        Analyzes 1m, 5m, and 15m timeframes simultaneously for superior accuracy.

        Args:
            data: DataFrame with OHLC data (1-minute candles)
                 Must have at least 75 rows for 15-minute analysis

        Returns:
            dict: {
                'signal': 'UP', 'DOWN', or 'NEUTRAL',
                'confidence': float between 0.0 and 1.0,
                'details': dict with comprehensive multi-timeframe analysis
            }
        """
        if len(data) < 30:  # Reduced data requirement for faster analysis
            return self._analyze_single_timeframe(data)  # Fallback to single timeframe

        try:
            # Analyze 1-minute timeframe (original data)
            tf1_result = self._analyze_single_timeframe(data)
            tf1_signal = Signal(tf1_result['signal'])
            tf1_confidence = tf1_result['confidence']

            # Resample to 5-minute timeframe (reduced requirements for speed)
            tf5_data = self.mtf_analyzer.resample_to_timeframe(data, 5)
            if tf5_data is not None and len(tf5_data) >= 6:  # Reduced from 15 to 6 for speed
                tf5_result = self._analyze_single_timeframe(tf5_data)
                tf5_signal = Signal(tf5_result['signal'])
                tf5_confidence = tf5_result['confidence']
            else:
                tf5_result = tf1_result.copy()
                tf5_signal = tf1_signal
                tf5_confidence = tf1_confidence * 0.8  # Reduce confidence for fallback

            # Resample to 15-minute timeframe (reduced requirements for speed)
            tf15_data = self.mtf_analyzer.resample_to_timeframe(data, 15)
            if tf15_data is not None and len(tf15_data) >= 2:  # Reduced from 5 to 2 for speed
                tf15_result = self._analyze_single_timeframe(tf15_data)
                tf15_signal = Signal(tf15_result['signal'])
                tf15_confidence = tf15_result['confidence']
            else:
                tf15_result = tf5_result.copy()
                tf15_signal = tf5_signal
                tf15_confidence = tf5_confidence * 0.8  # Reduce confidence for fallback

            # Analyze timeframe confluence
            final_signal, final_confidence = self.mtf_analyzer.analyze_timeframe_confluence(
                tf1_signal, tf1_confidence,
                tf5_signal, tf5_confidence,
                tf15_signal, tf15_confidence
            )

            # Get comprehensive analysis summary
            mtf_summary = self.mtf_analyzer.get_timeframe_analysis_summary(
                tf1_result, tf5_result, tf15_result
            )

            # Apply session and filtering (minimal)
            current_session = MarketSessionAnalyzer.get_current_market_session()
            session_characteristics = MarketSessionAnalyzer.get_session_characteristics(current_session)

            # Apply minimal session adjustment
            session_adjusted_confidence = final_confidence * session_characteristics['confidence_multiplier']
            session_adjusted_confidence = min(1.0, max(0.0, session_adjusted_confidence))

            # Compile comprehensive details
            details = {
                'multi_timeframe_analysis': mtf_summary,
                'individual_timeframes': {
                    '1m': tf1_result,
                    '5m': tf5_result,
                    '15m': tf15_result
                },
                'confluence_result': {
                    'signal': final_signal.value,
                    'confidence': final_confidence,
                    'method': 'multi_timeframe_confluence'
                },
                'market_session': {
                    'current_session': current_session.value,
                    'characteristics': session_characteristics,
                    'confidence_multiplier': session_characteristics['confidence_multiplier']
                },
                'final_adjustment': {
                    'pre_session': final_confidence,
                    'post_session': session_adjusted_confidence
                }
            }

            return {
                'signal': final_signal.value,
                'confidence': round(session_adjusted_confidence, 4),
                'details': details
            }

        except Exception as e:
            # Fallback to single timeframe analysis
            return self._analyze_single_timeframe(data)

    def predict(self, data: pd.DataFrame, asset: str = "UNKNOWN") -> Dict[str, any]:
        """
        🧠 AI-ENHANCED PREDICTION ENGINE WITH ADAPTIVE LEARNING 🧠

        Generate sophisticated trading predictions using multi-timeframe confluence
        analysis combined with advanced price action, candle psychology, and AI-like learning.

        Args:
            data: DataFrame with OHLC data (columns: open, high, low, close)
                 Must have at least 30 rows for analysis, 75+ for multi-timeframe
            asset: Asset name for learning-based adjustments

        Returns:
            dict: {
                'signal': 'UP', 'DOWN', or 'NEUTRAL',
                'confidence': float between 0.0 and 1.0,
                'details': dict with comprehensive analysis results,
                'ai_adjustments': dict with learning-based modifications
            }
        """
        # Get base prediction using existing logic
        if len(data) >= 75:
            base_prediction = self.predict_multi_timeframe(data)
        else:
            base_prediction = self._analyze_single_timeframe(data)

        # 🧠 AI-LIKE ENHANCEMENTS: Apply adaptive learning
        enhanced_prediction = self._apply_ai_learning(base_prediction, data, asset)

        return enhanced_prediction

    def _apply_ai_learning(self, base_prediction: Dict, data: pd.DataFrame, asset: str) -> Dict[str, any]:
        """🧠 Apply AI-like learning adjustments to base prediction"""
        try:
            # Get current market session
            current_session = MarketSessionAnalyzer.get_current_market_session()

            # Extract pattern information from prediction details
            pattern_detected = self._extract_dominant_pattern(base_prediction)

            # Check if we should trade this asset based on learning
            should_trade, asset_multiplier = self.learning_engine.should_trade_asset(asset)

            if not should_trade:
                # AI decides to avoid this asset
                return {
                    'signal': Signal.NEUTRAL.value,
                    'confidence': 0.0,
                    'details': base_prediction.get('details', {}),
                    'ai_adjustments': {
                        'asset_filtered': True,
                        'reason': 'Poor historical performance on this asset',
                        'asset_success_rate': self.learning_engine.asset_performance.get(asset, {}).get('success_rate', 0.0)
                    }
                }

            # Get AI-adjusted confidence
            base_confidence = base_prediction['confidence']
            ai_confidence = self.learning_engine.get_adaptive_confidence(
                pattern_detected, base_confidence, asset, current_session.value
            )

            # Apply asset performance multiplier
            final_confidence = ai_confidence * asset_multiplier

            # Get pattern weight adjustment
            pattern_weight = self.learning_engine.get_pattern_weight(pattern_detected)

            # Apply pattern weight to confidence
            final_confidence *= pattern_weight

            # Ensure confidence stays within bounds
            final_confidence = max(0.0, min(0.95, final_confidence))

            # Prepare AI adjustments summary
            ai_adjustments = {
                'asset_filtered': False,
                'pattern_detected': pattern_detected,
                'base_confidence': base_confidence,
                'ai_adjusted_confidence': ai_confidence,
                'asset_multiplier': asset_multiplier,
                'pattern_weight': pattern_weight,
                'final_confidence': final_confidence,
                'learning_insights': self.learning_engine.get_learning_insights(),
                'market_session': current_session.value
            }

            # Return enhanced prediction
            enhanced_prediction = base_prediction.copy()
            enhanced_prediction['confidence'] = final_confidence
            enhanced_prediction['ai_adjustments'] = ai_adjustments

            return enhanced_prediction

        except Exception as e:
            # Fallback to base prediction if AI enhancement fails
            base_prediction['ai_adjustments'] = {
                'error': f'AI enhancement failed: {str(e)}',
                'fallback_used': True
            }
            return base_prediction

    def _extract_dominant_pattern(self, prediction: Dict) -> str:
        """Extract the dominant pattern from prediction details"""
        try:
            details = prediction.get('details', {})

            # Check multi-timeframe analysis
            if 'multi_timeframe_analysis' in details:
                mtf_details = details['multi_timeframe_analysis']
                if 'dominant_pattern' in mtf_details:
                    return mtf_details['dominant_pattern']

            # Check individual timeframe details
            if 'individual_timeframes' in details:
                tf_details = details['individual_timeframes']
                for tf in ['1m', '5m', '15m']:
                    if tf in tf_details and 'details' in tf_details[tf]:
                        tf_analysis = tf_details[tf]['details']
                        # Look for pattern information in various analysis components
                        for component in ['candle_psychology', 'breakout_analysis', 'market_structure']:
                            if component in tf_analysis:
                                comp_details = tf_analysis[component]
                                if isinstance(comp_details, dict):
                                    if 'pattern' in comp_details:
                                        return comp_details['pattern']
                                    if 'pattern_detected' in comp_details:
                                        return comp_details['pattern_detected']

            # Fallback to signal type
            signal = prediction.get('signal', 'NEUTRAL')
            return f"{signal.lower()}_signal"

        except Exception:
            return 'unknown_pattern'

    def record_trading_outcome(self, asset: str, pattern_detected: str, signal: str,
                             confidence: float, outcome: str, market_volatility: float = 0.0):
        """🧠 Record trading outcome for AI learning"""
        try:
            current_session = MarketSessionAnalyzer.get_current_market_session()

            memory = TradingMemory(
                timestamp=datetime.now(),
                asset=asset,
                pattern_detected=pattern_detected,
                signal=signal,
                confidence=confidence,
                outcome=outcome,  # 'win', 'loss', 'pending'
                market_session=current_session.value,
                market_volatility=market_volatility
            )

            self.learning_engine.add_trading_memory(memory)

        except Exception as e:
            print(f"Error recording trading outcome: {e}")

    def get_ai_insights(self) -> Dict[str, any]:
        """🧠 Get AI learning insights and performance analytics"""
        return self.learning_engine.get_learning_insights()

    def should_trade_asset_ai(self, asset: str) -> Tuple[bool, float, str]:
        """🧠 AI decision on whether to trade specific asset with reasoning"""
        should_trade, multiplier = self.learning_engine.should_trade_asset(asset)

        if not should_trade:
            reason = "Poor historical performance - AI recommends avoiding this asset"
        elif multiplier < 0.8:
            reason = f"Reduced confidence due to {(1-multiplier)*100:.1f}% lower success rate"
        elif multiplier > 1.2:
            reason = f"Increased confidence due to {(multiplier-1)*100:.1f}% higher success rate"
        else:
            reason = "Normal trading conditions based on historical performance"

        return should_trade, multiplier, reason

    def _get_market_context(self, data: pd.DataFrame) -> Dict[str, any]:
        """Get additional market context for the summary"""
        try:
            recent_data = data.tail(10)
            volatility = recent_data['close'].std() / recent_data['close'].mean()
            price_change = (data['close'].iloc[-1] - data['close'].iloc[-10]) / data['close'].iloc[-10]

            return {
                'volatility': round(volatility, 4),
                'recent_price_change': round(price_change, 4),
                'current_price': round(data['close'].iloc[-1], 5),
                'candles_analyzed': len(data)
            }
        except:
            return {'error': 'Could not calculate market context'}

    def _calculate_volatility_score(self, data: pd.DataFrame) -> float:
        """Calculate normalized volatility score for expiry prediction"""
        try:
            recent_data = data.tail(20)

            # Calculate multiple volatility measures
            price_volatility = recent_data['close'].std() / recent_data['close'].mean()
            range_volatility = (recent_data['high'] - recent_data['low']).std() / recent_data['close'].mean()

            # Combine volatility measures
            combined_volatility = (price_volatility * 0.6) + (range_volatility * 0.4)

            # Normalize to 0-1 scale (typical forex volatility ranges)
            normalized_volatility = min(1.0, combined_volatility / 0.03)

            return normalized_volatility

        except Exception:
            return 0.5  # Default medium volatility

    def _get_momentum_strength_score(self, data: pd.DataFrame) -> float:
        """Get normalized momentum strength score for expiry prediction"""
        try:
            momentum_analysis = self._analyze_momentum(data)

            # Extract momentum components
            range_expansion = momentum_analysis.get('range_expansion', 0)
            body_momentum = momentum_analysis.get('body_momentum', 0)
            wick_momentum = momentum_analysis.get('wick_momentum', 0)

            # Calculate combined momentum strength
            momentum_strength = abs(range_expansion * 0.4 + body_momentum * 0.4 + wick_momentum * 0.2)

            # Normalize to 0-1 scale
            return min(1.0, momentum_strength)

        except Exception:
            return 0.5  # Default medium momentum

    def _identify_market_pattern_type(self, data: pd.DataFrame) -> str:
        """Identify the current market pattern type for expiry prediction"""
        try:
            # Analyze market structure
            structure_analysis = self._analyze_market_structure(data)
            breakout_analysis = self._analyze_breakouts(data)

            # Check for breakout patterns
            if breakout_analysis.get('signal') != Signal.NEUTRAL.value:
                breakout_strength = breakout_analysis.get('confidence', 0)
                if breakout_strength > 0.7:
                    return "strong_breakout"
                elif breakout_strength > 0.5:
                    return "weak_breakout"

            # Check trend strength
            trend_strength = structure_analysis.get('confidence', 0)
            if trend_strength > 0.7:
                return "strong_trend"
            elif trend_strength > 0.4:
                return "weak_trend"
            else:
                return "range_bound"

        except Exception:
            return "neutral"

    def _calculate_support_resistance_distance(self, data: pd.DataFrame) -> float:
        """Calculate distance to nearest significant support/resistance level"""
        try:
            sr_analysis = self._analyze_support_resistance(data)
            current_price = data['close'].iloc[-1]

            # Get support and resistance levels from analysis
            levels = []
            if 'support_levels' in sr_analysis.get('details', {}):
                levels.extend(sr_analysis['details']['support_levels'])
            if 'resistance_levels' in sr_analysis.get('details', {}):
                levels.extend(sr_analysis['details']['resistance_levels'])

            if not levels:
                return 0.5  # Default medium distance

            # Find closest level
            distances = [abs(current_price - level) / current_price for level in levels]
            min_distance = min(distances) if distances else 0.02

            # Normalize distance (typical significant levels are 0.5-2% away)
            normalized_distance = min(1.0, min_distance / 0.02)

            return normalized_distance

        except Exception:
            return 0.5  # Default medium distance

    def predict_optimal_expiry(self, data: pd.DataFrame, signal_confidence: float) -> int:
        """
        🕐 INTELLIGENT EXPIRY TIME PREDICTION 🕐

        Predict optimal expiry time based on comprehensive market analysis.
        Uses volatility, momentum, pattern type, and confidence to determine
        the best timeframe for the predicted signal.

        Args:
            data: DataFrame with OHLC data
            signal_confidence: Confidence level of the trading signal (0.0-1.0)

        Returns:
            int: Optimal expiry time in seconds (30, 60, or 90 seconds)
        """
        try:
            # Calculate market condition factors
            volatility_score = self._calculate_volatility_score(data)
            momentum_score = self._get_momentum_strength_score(data)
            pattern_type = self._identify_market_pattern_type(data)
            sr_distance = self._calculate_support_resistance_distance(data)

            # Base expiry calculation using market conditions
            base_expiry = self._calculate_base_expiry(
                volatility_score, momentum_score, pattern_type, sr_distance
            )

            # Adjust based on signal confidence
            confidence_adjusted_expiry = self._adjust_expiry_for_confidence(
                base_expiry, signal_confidence
            )

            # Apply session-aware expiry adjustment
            current_session = MarketSessionAnalyzer.get_current_market_session()
            session_adjusted_expiry = MarketSessionAnalyzer.get_session_optimal_expiry(
                current_session, confidence_adjusted_expiry
            )

            # Ensure expiry is within valid range and available options
            final_expiry = self._validate_expiry_time(session_adjusted_expiry)

            return final_expiry

        except Exception as e:
            # Fallback to safe default
            return 60  # 1 minute default

    def _calculate_base_expiry(self, volatility: float, momentum: float,
                              pattern_type: str, sr_distance: float) -> int:
        """Calculate base expiry time from market conditions - ENHANCED FOR 60s/90s ONLY"""

        # Enhanced pattern-based base expiry (60s, 90s ONLY) - Optimized for accuracy
        pattern_expiry_map = {
            "strong_breakout": 60,    # Quick moves expected - 60s optimal
            "weak_breakout": 90,      # Moderate moves - 90s for development
            "strong_trend": 60,       # Quick sustained moves - 60s optimal
            "weak_trend": 90,         # Slower trend moves - 90s for development
            "range_bound": 60,        # Range-bound markets - 60s for quick reversals
            "neutral": 60             # Default quick time for maximum accuracy
        }

        base_expiry = pattern_expiry_map.get(pattern_type, 60)

        # Enhanced volatility adjustments (60s, 90s ONLY) - Optimized for accuracy
        if volatility > 0.7:          # Very high volatility
            base_expiry = 60          # Quick moves need quick expiry
        elif volatility > 0.5:        # High volatility
            base_expiry = 60          # Still prefer quick expiry for accuracy
        elif volatility > 0.3:        # Moderate volatility
            base_expiry = min(base_expiry, 90)   # Cap at 90 seconds
        else:                         # Low volatility
            base_expiry = 90          # Use 90s for slow moves

        # Enhanced momentum adjustments (60s, 90s ONLY) - Optimized for accuracy
        if momentum > 0.8:            # Very strong momentum
            base_expiry = 60          # Quick moves need quick expiry
        elif momentum > 0.6:          # Strong momentum
            base_expiry = 60          # Prefer quick expiry for strong momentum
        elif momentum > 0.4:          # Moderate momentum
            base_expiry = min(base_expiry, 90)   # Cap at 90 seconds
        else:                         # Weak momentum
            base_expiry = 90          # Use 90s for weak momentum

        # Enhanced Support/Resistance distance adjustments (60s, 90s ONLY)
        if sr_distance < 0.2:         # Very close to S/R level
            base_expiry = 60          # Quick reaction expected
        elif sr_distance < 0.4:       # Close to S/R level
            base_expiry = min(base_expiry, 90)   # Quick to moderate reaction
        else:                         # Far from S/R level
            base_expiry = 90          # Use 90s for moves away from levels

        # Final validation - ensure only 60s or 90s
        return 60 if base_expiry <= 75 else 90

    def _adjust_expiry_for_confidence(self, base_expiry: int, confidence: float) -> int:
        """Adjust expiry time based on signal confidence (60s, 90s, 120s) - 30s trades fail"""

        if confidence >= 0.9:         # Very high confidence
            return base_expiry        # Use calculated time
        elif confidence >= 0.8:       # High confidence
            return base_expiry        # Use calculated time
        elif confidence >= 0.7:       # Medium-high confidence
            return min(120, base_expiry + 30)    # Add small buffer (cap at 120s)
        elif confidence >= 0.6:       # Medium confidence
            return 120                # Use maximum available for safety
        else:                         # Lower confidence
            return 120                # Use maximum available for safety

    def _validate_expiry_time(self, expiry: int) -> int:
        """Validate and round expiry to available options"""

        # Available expiry times (in seconds) - 60s, 90s, 120s (30s trades fail with "expiration" error)
        available_expiries = [60, 90, 120]

        # Find closest available expiry
        closest_expiry = min(available_expiries, key=lambda x: abs(x - expiry))

        # Ensure within bounds
        if closest_expiry < 60:
            return 60
        elif closest_expiry > 120:
            return 120

        return closest_expiry

    def predict_with_expiry(self, data: pd.DataFrame) -> Dict[str, any]:
        """
        🧠🕐 ENHANCED PREDICTION WITH OPTIMAL EXPIRY TIME 🕐🧠

        Generate comprehensive trading prediction including optimal expiry time.
        Combines intelligent signal prediction with smart timing analysis.

        Args:
            data: DataFrame with OHLC data

        Returns:
            dict: {
                'signal': 'UP', 'DOWN', or 'NEUTRAL',
                'confidence': float between 0.0 and 1.0,
                'optimal_expiry': int (expiry time in seconds),
                'expiry_reasoning': dict with expiry calculation details,
                'details': dict with comprehensive analysis results
            }
        """
        # Get base prediction
        prediction = self.predict(data)

        if prediction['signal'] == Signal.NEUTRAL.value:
            # For neutral signals, use default expiry
            prediction['optimal_expiry'] = 60
            prediction['expiry_reasoning'] = {
                'reason': 'neutral_signal',
                'base_expiry': 60,
                'adjustments': 'none'
            }
            return prediction

        # Calculate optimal expiry for actionable signals
        optimal_expiry = self.predict_optimal_expiry(data, prediction['confidence'])

        # Add expiry information to prediction
        prediction['optimal_expiry'] = optimal_expiry
        prediction['expiry_reasoning'] = self._get_expiry_reasoning(data, prediction['confidence'])

        return prediction

    def _get_expiry_reasoning(self, data: pd.DataFrame, confidence: float) -> Dict[str, any]:
        """Get detailed reasoning for expiry time selection"""
        try:
            volatility = self._calculate_volatility_score(data)
            momentum = self._get_momentum_strength_score(data)
            pattern = self._identify_market_pattern_type(data)
            sr_distance = self._calculate_support_resistance_distance(data)

            return {
                'volatility_score': round(volatility, 3),
                'momentum_score': round(momentum, 3),
                'pattern_type': pattern,
                'sr_distance_score': round(sr_distance, 3),
                'signal_confidence': round(confidence, 3),
                'reasoning': f"Pattern: {pattern}, Vol: {volatility:.2f}, Mom: {momentum:.2f}"
            }
        except Exception:
            return {'error': 'Could not calculate expiry reasoning'}

    @staticmethod
    def format_expiry_time(seconds: int) -> str:
        """Format expiry time in a human-readable way"""
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            if remaining_seconds == 0:
                return f"{minutes}min"
            else:
                return f"{minutes}min {remaining_seconds}s"
        else:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            if remaining_minutes == 0:
                return f"{hours}hr"
            else:
                return f"{hours}hr {remaining_minutes}min"


# Create alias for backward compatibility
RuleBasedModel = IntelligentRuleBasedModel


def generate_mock_data(num_candles: int = 100, base_price: float = 1.2000) -> pd.DataFrame:
    """Generate realistic mock OHLC data for testing the intelligent model"""
    np.random.seed(42)  # For reproducible results

    data = []
    current_price = base_price

    for i in range(num_candles):
        # Generate realistic price movement with trends
        if i < 30:
            # Initial uptrend
            trend_bias = 0.0002
        elif i < 60:
            # Sideways movement
            trend_bias = 0.0
        else:
            # Downtrend
            trend_bias = -0.0001

        change_percent = np.random.normal(trend_bias, 0.001)  # Add trend bias
        price_change = current_price * change_percent

        # Create OHLC for this candle
        open_price = current_price
        close_price = current_price + price_change

        # Generate high and low with realistic wicks
        wick_factor = np.random.uniform(0.3, 0.8)
        high_price = max(open_price, close_price) + abs(np.random.normal(0, current_price * 0.0005)) * wick_factor
        low_price = min(open_price, close_price) - abs(np.random.normal(0, current_price * 0.0005)) * wick_factor

        # Occasionally create strong candles (for pattern detection)
        if np.random.random() < 0.1:  # 10% chance
            if np.random.random() < 0.5:
                # Strong bullish candle
                close_price = open_price + abs(np.random.normal(0, current_price * 0.002))
                high_price = close_price + abs(np.random.normal(0, current_price * 0.0003))
            else:
                # Strong bearish candle
                close_price = open_price - abs(np.random.normal(0, current_price * 0.002))
                low_price = close_price - abs(np.random.normal(0, current_price * 0.0003))

        data.append({
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5)
        })

        current_price = close_price

    return pd.DataFrame(data)


if __name__ == "__main__":
    """
    🧠 INTELLIGENT RULE-BASED TRADING MODEL DEMO 🧠

    Demonstration of advanced price action and candle psychology analysis
    that mimics trained AI behavior using sophisticated rule-based logic.
    """
    print("🧠 INTELLIGENT RULE-BASED TRADING MODEL DEMO 🧠")
    print("=" * 60)
    print("🎯 Advanced Price Action & Candle Psychology Analysis")
    print("🚀 Mimics Trained AI Behavior with Rule-Based Logic")
    print("💎 Perfect for Binary Options Trading (1-2 min expiry)")
    print("=" * 60)

    # Initialize the intelligent model
    model = IntelligentRuleBasedModel(confidence_threshold=0.75)
    print(f"\n✅ Intelligent Model Initialized")
    print(f"   📊 Confidence Threshold: {model.confidence_threshold:.1%}")
    print(f"   🔧 Analysis Components: {len(model.analysis_weights)}")

    # Display analysis weights
    print(f"\n🎯 ANALYSIS WEIGHTS:")
    for name, weight in model.analysis_weights.items():
        print(f"   {name.replace('_', ' ').title():20} | Weight: {weight:.1f}")

    # Generate realistic mock data with patterns
    print(f"\n📊 Generating Realistic Market Data...")
    mock_data = generate_mock_data(num_candles=100, base_price=1.2000)
    print(f"   ✅ Generated {len(mock_data)} candles with realistic patterns")
    print(f"   📈 Price Range: {mock_data['low'].min():.5f} - {mock_data['high'].max():.5f}")
    print(f"   📊 Price Change: {((mock_data['close'].iloc[-1] / mock_data['close'].iloc[0]) - 1) * 100:+.2f}%")

    # Run intelligent prediction with expiry
    print(f"\n🔮 Running Enhanced Intelligent Analysis with Expiry Prediction...")
    result = model.predict_with_expiry(mock_data)

    # Display comprehensive results
    print(f"\n🎯 ENHANCED PREDICTION RESULTS:")
    print(f"   🚦 Signal: {result['signal']}")
    print(f"   🎯 Confidence: {result['confidence']:.1%}")
    print(f"   🕐 Optimal Expiry: {result.get('optimal_expiry', 60)}s ({result.get('optimal_expiry', 60)//60}min {result.get('optimal_expiry', 60)%60}s)")

    # Show market session information
    if 'details' in result and 'market_session' in result['details']:
        session_info = result['details']['market_session']
        print(f"\n🌍 MARKET SESSION ANALYSIS:")
        print(f"   📍 Current Session: {session_info['current_session']}")
        print(f"   📊 Volatility: {session_info['characteristics']['volatility']}")
        print(f"   🎯 Strategy: {session_info['characteristics']['preferred_strategy']}")
        print(f"   🔢 Confidence Multiplier: {session_info['confidence_multiplier']:.1f}x")
        print(f"   💡 Description: {session_info['characteristics']['description']}")

    # Show false signal analysis
    if 'details' in result and 'false_signal_analysis' in result['details']:
        false_analysis = result['details']['false_signal_analysis']
        print(f"\n🛡️ FALSE SIGNAL ANALYSIS:")
        print(f"   🚨 Risk Score: {false_analysis['risk_score']:.2f}")
        print(f"   🔍 Risk Patterns: {len(false_analysis['patterns'])}")
        if false_analysis['patterns']:
            print(f"   ⚠️ Detected Patterns: {', '.join(false_analysis['patterns'])}")
        print(f"   🎯 Risk Multiplier: {false_analysis['risk_multiplier']:.2f}x")

    # Show signal progression
    if 'details' in result and 'signal_progression' in result['details']:
        progression = result['details']['signal_progression']
        print(f"\n🔄 SIGNAL PROGRESSION:")
        print(f"   1️⃣ Initial: {progression['initial']['signal']} ({progression['initial']['confidence']:.1%})")
        print(f"   2️⃣ Filtered: {progression['filtered']['signal']} ({progression['filtered']['confidence']:.1%})")
        print(f"   3️⃣ Final: {progression['final']['signal']} ({progression['final']['confidence']:.1%})")

    # Show expiry reasoning
    if 'expiry_reasoning' in result:
        expiry_info = result['expiry_reasoning']
        print(f"\n🕐 EXPIRY TIME ANALYSIS:")
        if 'pattern_type' in expiry_info:
            print(f"   📊 Market Pattern: {expiry_info['pattern_type']}")
            print(f"   🌊 Volatility Score: {expiry_info.get('volatility_score', 0):.3f}")
            print(f"   ⚡ Momentum Score: {expiry_info.get('momentum_score', 0):.3f}")
            print(f"   🎯 S/R Distance: {expiry_info.get('sr_distance_score', 0):.3f}")
            print(f"   💡 Reasoning: {expiry_info.get('reasoning', 'N/A')}")

    if 'details' in result and 'intelligent_summary' in result['details']:
        summary = result['details']['intelligent_summary']
        print(f"\n📊 INTELLIGENT SCORE BREAKDOWN:")
        print(f"   📈 UP Score: {summary['up_score']:.3f}")
        print(f"   📉 DOWN Score: {summary['down_score']:.3f}")
        print(f"   ⚪ NEUTRAL Score: {summary['neutral_score']:.3f}")
        print(f"   🎯 High Confidence Signals: {summary['high_confidence_signals']}")

        # Market context
        if 'market_context' in summary:
            context = summary['market_context']
            print(f"\n🌍 MARKET CONTEXT:")
            print(f"   📊 Volatility: {context.get('volatility', 0):.4f}")
            print(f"   📈 Recent Change: {context.get('recent_price_change', 0):+.2%}")
            print(f"   💰 Current Price: {context.get('current_price', 0):.5f}")

        print(f"\n🔍 DETAILED ANALYSIS BREAKDOWN:")
        for name, details in result['details'].items():
            if name not in ['intelligent_summary']:
                signal_emoji = "📈" if details['signal'] == 'UP' else "📉" if details['signal'] == 'DOWN' else "⚪"
                print(f"   {signal_emoji} {name:20} | {details['signal']:7} | Conf: {details['confidence']:.3f} | Weight: {details['weight']:.1f}")

                # Show some analysis details if available
                if 'analysis_details' in details and details['analysis_details']:
                    analysis_details = details['analysis_details']
                    if isinstance(analysis_details, dict):
                        for key, value in list(analysis_details.items())[:2]:  # Show first 2 details
                            if isinstance(value, (int, float)):
                                print(f"      └─ {key}: {value:.3f}")
                            elif isinstance(value, str):
                                print(f"      └─ {key}: {value}")

    # Test edge cases
    print(f"\n🧪 TESTING EDGE CASES:")

    # Test with insufficient data
    print(f"   📊 Testing with insufficient data...")
    small_data = mock_data.head(10)
    small_result = model.predict(small_data)
    print(f"      Result: {small_result['signal']} (Confidence: {small_result['confidence']:.3f})")

    # Test with different confidence thresholds
    print(f"   🎯 Testing with strict confidence threshold (90%)...")
    strict_model = IntelligentRuleBasedModel(confidence_threshold=0.9)
    strict_result = strict_model.predict(mock_data)
    print(f"      Result: {strict_result['signal']} (Confidence: {strict_result['confidence']:.3f})")

    print(f"\n✨ INTELLIGENT DEMO COMPLETED SUCCESSFULLY!")
    print(f"🎯 Key Features Demonstrated:")
    print(f"   ✅ Market Structure Analysis (HH/HL, LH/LL)")
    print(f"   ✅ Support/Resistance Level Detection")
    print(f"   ✅ Breakout & Fakeout Analysis")
    print(f"   ✅ Advanced Candle Psychology")
    print(f"   ✅ Supply/Demand Zone Analysis")
    print(f"   ✅ Momentum & Range Analysis")
    print(f"   ✅ Intelligent Confidence Boosting")
    print(f"   ✅ 🌍 Market Session Awareness")
    print(f"   ✅ 🛡️ False Signal Filtering")
    print(f"   ✅ 🕐 Session-Optimized Expiry Times")
    print(f"   ✅ Comprehensive Error Handling")

    print(f"\n💡 INTEGRATION READY:")
    print(f"   🚀 Perfect for Quotex API Integration")
    print(f"   ⚡ Optimized for 1-2 minute binary options")
    print(f"   🧠 Mimics trained AI with rule-based logic")
    print(f"   📊 No ML dependencies - Pure pandas/numpy")
    print(f"   🎯 Professional-grade analysis engine")
    print(f"\n🎉 Ready for your automation bot!")